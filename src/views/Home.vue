<template>
  <div class="home-page">
    <header class="header">
      <div class="header-content">
        <h1>欢迎来到首页</h1>
        <div class="user-info">
          <img :src="authStore.userInfo?.avatar" alt="头像" class="avatar">
          <span class="username">{{ authStore.userInfo?.username }}</span>
          <button @click="handleLogout" class="logout-btn">退出登录</button>
        </div>
      </div>
    </header>

    <main class="main-content">
      <div class="welcome-card">
        <h2>🎉 登录成功！</h2>
        <p>您已成功登录系统，现在可以访问所有功能。</p>
        <div class="user-details">
          <p><strong>用户名：</strong>{{ authStore.userInfo?.username }}</p>
          <p><strong>邮箱：</strong>{{ authStore.userInfo?.email }}</p>
          <p><strong>角色：</strong>{{ authStore.userInfo?.role }}</p>
        </div>
      </div>

      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">📊</div>
          <h3>数据分析</h3>
          <p>查看详细的数据分析报告</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">👥</div>
          <h3>用户管理</h3>
          <p>管理系统用户和权限</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">⚙️</div>
          <h3>系统设置</h3>
          <p>配置系统参数和选项</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">📈</div>
          <h3>报表中心</h3>
          <p>生成和查看各种报表</p>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const handleLogout = () => {
  authStore.logout()
  router.push('/login')
}
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  padding: 20px 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h1 {
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.username {
  color: #333;
  font-weight: 500;
}

.logout-btn {
  padding: 8px 16px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.welcome-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  margin-bottom: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.welcome-card h2 {
  color: #333;
  font-size: 32px;
  margin-bottom: 15px;
}

.welcome-card p {
  color: #666;
  font-size: 16px;
  margin-bottom: 30px;
}

.user-details {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.user-details p {
  margin-bottom: 10px;
  color: #333;
}

.user-details p:last-child {
  margin-bottom: 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 40px;
}

.feature-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.feature-card h3 {
  color: #333;
  font-size: 20px;
  margin-bottom: 10px;
  font-weight: 600;
}

.feature-card p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 15px;
  }

  .user-info {
    flex-direction: column;
    gap: 10px;
  }

  .welcome-card {
    padding: 30px 20px;
  }

  .welcome-card h2 {
    font-size: 24px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }
}
</style> 