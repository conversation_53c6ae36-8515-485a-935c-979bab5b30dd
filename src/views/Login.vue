<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-header">
        <h1>欢迎回来</h1>
        <p>请登录您的账户</p>
      </div>

      <!-- 消息提示 -->
      <div v-if="message" class="message" :class="messageType">
        {{ message }}
      </div>

      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label for="username">用户名</label>
          <input 
            type="text" 
            id="username" 
            v-model="form.username" 
            @blur="validateUsername"
            :class="{ error: errors.username }"
            placeholder="请输入用户名或邮箱"
            autocomplete="username"
            :disabled="authStore.isLoading"
          >
          <div v-if="errors.username" class="error-message">
            {{ errors.username }}
          </div>
        </div>

        <div class="form-group">
          <label for="password">密码</label>
          <div class="password-input">
            <input 
              :type="showPassword ? 'text' : 'password'" 
              id="password" 
              v-model="form.password" 
              @blur="validatePassword"
              :class="{ error: errors.password }"
              placeholder="请输入密码"
              autocomplete="current-password"
              :disabled="authStore.isLoading"
            >
            <button 
              type="button" 
              class="password-toggle" 
              @click="togglePassword"
              :title="showPassword ? '隐藏密码' : '显示密码'"
            >
              <span v-if="showPassword">👁️</span>
              <span v-else>👁️‍🗨️</span>
            </button>
          </div>
          <div v-if="errors.password" class="error-message">
            {{ errors.password }}
          </div>
        </div>

        <div class="form-options">
          <label class="checkbox-label">
            <input 
              type="checkbox" 
              v-model="form.remember"
              :disabled="authStore.isLoading"
            >
            <span class="checkmark"></span>
            记住我
          </label>
          <a href="#" @click.prevent="forgotPassword" class="forgot-link">
            忘记密码？
          </a>
        </div>

        <button 
          type="submit" 
          class="login-button" 
          :class="{ loading: authStore.isLoading }"
          :disabled="authStore.isLoading"
        >
          <span v-if="authStore.isLoading" class="spinner"></span>
          {{ authStore.isLoading ? '登录中...' : '登录' }}
        </button>
      </form>

      <div class="divider">
        <span>或</span>
      </div>

      <div class="social-login">
        <button 
          class="social-button wechat" 
          @click="socialLogin('wechat')"
          :disabled="authStore.isLoading"
        >
          <span class="icon">💬</span>
          微信登录
        </button>
        <button 
          class="social-button qq" 
          @click="socialLogin('qq')"
          :disabled="authStore.isLoading"
        >
          <span class="icon">🐧</span>
          QQ登录
        </button>
      </div>

      <div class="signup-link">
        还没有账户？ 
        <a href="#" @click.prevent="goToSignup">立即注册</a>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表单数据
const form = reactive({
  username: '',
  password: '',
  remember: false
})

// 状态
const showPassword = ref(false)
const message = ref('')
const messageType = ref('')

// 错误信息
const errors = reactive({
  username: '',
  password: ''
})

// 验证用户名
const validateUsername = () => {
  errors.username = ''
  if (!form.username.trim()) {
    errors.username = '请输入用户名'
    return false
  }
  if (form.username.length < 3) {
    errors.username = '用户名至少需要3个字符'
    return false
  }
  return true
}

// 验证密码
const validatePassword = () => {
  errors.password = ''
  if (!form.password) {
    errors.password = '请输入密码'
    return false
  }
  if (form.password.length < 6) {
    errors.password = '密码至少需要6个字符'
    return false
  }
  return true
}

// 切换密码显示
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

// 显示消息
const showMessage = (text, type = 'info') => {
  message.value = text
  messageType.value = type
  setTimeout(() => {
    message.value = ''
    messageType.value = ''
  }, 3000)
}

// 处理登录
const handleLogin = async () => {
  // 验证表单
  const isUsernameValid = validateUsername()
  const isPasswordValid = validatePassword()

  if (!isUsernameValid || !isPasswordValid) {
    return
  }

  // 调用登录
  const result = await authStore.login({
    username: form.username,
    password: form.password
  })

  if (result.success) {
    showMessage(result.message, 'success')
    // 跳转到首页
    setTimeout(() => {
      router.push('/home')
    }, 1000)
  } else {
    showMessage(result.message, 'error')
  }
}

// 忘记密码
const forgotPassword = () => {
  showMessage('忘记密码功能：在实际应用中这里会跳转到密码重置页面', 'info')
}

// 社交登录
const socialLogin = (platform) => {
  const platformName = platform === 'wechat' ? '微信' : 'QQ'
  showMessage(`${platformName}登录功能：在实际应用中这里会调用相应的OAuth接口`, 'info')
}

// 跳转到注册
const goToSignup = () => {
  showMessage('注册功能：在实际应用中这里会跳转到注册页面', 'info')
}

// 组件挂载时初始化
onMounted(() => {
  authStore.initUser()
})
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h1 {
  color: #333;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
}

.login-header p {
  color: #666;
  font-size: 14px;
}

.message {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
}

.message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.message.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.login-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 500;
  font-size: 14px;
}

.form-group input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  background: #fff;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input.error {
  border-color: #e74c3c;
  background: #fff5f5;
}

.form-group input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  font-size: 18px;
  padding: 4px;
}

.password-toggle:hover {
  color: #333;
}

.error-message {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 5px;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #666;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
  accent-color: #667eea;
}

.forgot-link {
  color: #667eea;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.forgot-link:hover {
  color: #764ba2;
}

.login-button {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.login-button:active:not(:disabled) {
  transform: translateY(0);
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.divider {
  margin: 20px 0;
  text-align: center;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e1e5e9;
}

.divider span {
  background: rgba(255, 255, 255, 0.95);
  padding: 0 15px;
  color: #666;
  font-size: 12px;
}

.social-login {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.social-button {
  flex: 1;
  padding: 12px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  background: white;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.social-button:hover:not(:disabled) {
  border-color: #667eea;
  background: #f8f9ff;
}

.social-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.social-button.wechat {
  color: #07c160;
}

.social-button.qq {
  color: #12b7f5;
}

.social-button .icon {
  font-size: 16px;
}

.signup-link {
  text-align: center;
  font-size: 14px;
  color: #666;
}

.signup-link a {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
}

.signup-link a:hover {
  color: #764ba2;
}

@media (max-width: 480px) {
  .login-container {
    padding: 30px 20px;
    margin: 10px;
  }

  .login-header h1 {
    font-size: 24px;
  }

  .social-login {
    flex-direction: column;
  }
}
</style> 