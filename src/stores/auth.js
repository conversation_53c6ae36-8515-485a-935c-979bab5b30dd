import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || '')
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const userInfo = computed(() => user.value)

  // 登录
  const login = async (credentials) => {
    isLoading.value = true
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // 模拟验证
      if (credentials.username === 'admin' && credentials.password === '123456') {
        const mockToken = 'mock-jwt-token-' + Date.now()
        const mockUser = {
          id: 1,
          username: credentials.username,
          email: '<EMAIL>',
          avatar: 'https://via.placeholder.com/40',
          role: 'admin'
        }
        
        // 保存到本地存储
        localStorage.setItem('token', mockToken)
        localStorage.setItem('user', JSON.stringify(mockUser))
        
        // 更新状态
        token.value = mockToken
        user.value = mockUser
        
        return { success: true, message: '登录成功' }
      } else {
        throw new Error('用户名或密码错误')
      }
    } catch (error) {
      return { success: false, message: error.message }
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = () => {
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    token.value = ''
    user.value = null
  }

  // 初始化用户信息
  const initUser = () => {
    const savedUser = localStorage.getItem('user')
    if (savedUser) {
      user.value = JSON.parse(savedUser)
    }
  }

  // 检查token是否有效
  const checkAuth = async () => {
    if (!token.value) return false
    
    try {
      // 模拟验证token
      await new Promise(resolve => setTimeout(resolve, 500))
      return true
    } catch (error) {
      logout()
      return false
    }
  }

  return {
    user,
    token,
    isLoading,
    isAuthenticated,
    userInfo,
    login,
    logout,
    initUser,
    checkAuth
  }
}) 