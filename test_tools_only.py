#!/usr/bin/env python3
import asyncio
import sys
import os

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from client import MCPClient

async def test_tools_only():
    """Test just the MCP server connection and tool listing"""
    client = MCPClient()
    
    try:
        server_path = os.path.join(os.path.dirname(__file__), "fast_server.py")
        print(f"Connecting to FastMCP server: {server_path}")
        await client.connect_to_server(server_path)
        
        # Test direct tool call without <PERSON>
        print("\n=== Testing direct tool calls ===")
        
        # Test echo tool
        result1 = await client.session.call_tool("echo", {"message": "Hello direct call!"})
        print(f"Echo result: {result1.content}")
        
        # Test add_numbers tool
        result2 = await client.session.call_tool("add_numbers", {"a": 15, "b": 27})
        print(f"Add result: {result2.content}")
        
        # Test fetch_webpage tool
        result3 = await client.session.call_tool("fetch_webpage", {"url": "https://httpbin.org/html"})
        print(f"Fetch result: {result3.content}")
        
        print("\n=== Direct tool tests completed successfully! ===")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await client.cleanup()

if __name__ == "__main__":
    asyncio.run(test_tools_only())