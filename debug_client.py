#!/usr/bin/env python3
"""
调试 client.py 的专用脚本
包含详细的日志记录、错误处理和调试功能
"""

import asyncio
import sys
import os
import logging
import traceback
from typing import Optional

# 设置详细的日志记录
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from client import MCPClient

class DebugMCPClient(MCPClient):
    """扩展的MCP客户端，包含调试功能"""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger('DebugMCPClient')
        self.debug_mode = True
    
    async def connect_to_server(self, server_script_path: str):
        """连接服务器并记录详细信息"""
        self.logger.info(f"尝试连接到服务器: {server_script_path}")
        
        try:
            # 检查服务器文件是否存在
            if not os.path.exists(server_script_path):
                raise FileNotFoundError(f"服务器文件不存在: {server_script_path}")
            
            self.logger.info(f"服务器文件存在，大小: {os.path.getsize(server_script_path)} bytes")
            
            # 调用父类方法
            await super().connect_to_server(server_script_path)
            self.logger.info("成功连接到服务器")
            
        except Exception as e:
            self.logger.error(f"连接服务器失败: {e}")
            self.logger.error(traceback.format_exc())
            raise
    
    async def process_query(self, query: str) -> str:
        """处理查询并记录详细信息"""
        self.logger.info(f"处理查询: {query}")
        
        try:
            # 检查会话状态
            if not self.session:
                raise RuntimeError("MCP会话未初始化")
            
            self.logger.debug("获取可用工具列表")
            response = await self.session.list_tools()
            available_tools = [{
                "name": tool.name,
                "description": tool.description,
                "input_schema": tool.inputSchema
            } for tool in response.tools]
            
            self.logger.info(f"可用工具: {[tool['name'] for tool in available_tools]}")
            
            # 调用父类方法
            result = await super().process_query(query)
            self.logger.info("查询处理完成")
            return result
            
        except Exception as e:
            self.logger.error(f"处理查询失败: {e}")
            self.logger.error(traceback.format_exc())
            raise

async def test_connection():
    """测试基本连接"""
    print("=== 测试基本连接 ===")
    client = DebugMCPClient()
    
    try:
        # 测试简单服务器
        server_path = os.path.join(os.path.dirname(__file__), "simple_server.py")
        print(f"连接到简单服务器: {server_path}")
        await client.connect_to_server(server_path)
        print("✓ 连接成功")
        
        # 测试简单查询
        print("\n测试简单查询...")
        response = await client.process_query("Use the echo tool with message 'Hello Debug!'")
        print(f"响应: {response}")
        
    except Exception as e:
        print(f"✗ 连接测试失败: {e}")
        traceback.print_exc()
    finally:
        await client.cleanup()

async def test_web_scraper():
    """测试网页爬虫服务器"""
    print("\n=== 测试网页爬虫服务器 ===")
    client = DebugMCPClient()
    
    try:
        # 测试爬虫服务器
        server_path = os.path.join(os.path.dirname(__file__), "server.py")
        print(f"连接到爬虫服务器: {server_path}")
        await client.connect_to_server(server_path)
        print("✓ 连接成功")
        
        # 测试网页抓取
        print("\n测试网页抓取...")
        response = await client.process_query("""
        Use the scrape_url tool to fetch content from "https://httpbin.org/html" 
        and extract all paragraph text using CSS selector "p"
        """)
        print(f"响应: {response}")
        
    except Exception as e:
        print(f"✗ 爬虫测试失败: {e}")
        traceback.print_exc()
    finally:
        await client.cleanup()

async def interactive_debug():
    """交互式调试模式"""
    print("\n=== 交互式调试模式 ===")
    print("输入 'quit' 退出，输入 'help' 查看帮助")
    
    client = DebugMCPClient()
    
    try:
        # 选择服务器
        print("\n可用的服务器:")
        print("1. simple_server.py - 简单测试服务器")
        print("2. server.py - 网页爬虫服务器")
        print("3. fast_server.py - 快速服务器")
        
        choice = input("\n选择服务器 (1-3): ").strip()
        
        server_map = {
            "1": "simple_server.py",
            "2": "server.py", 
            "3": "fast_server.py"
        }
        
        if choice not in server_map:
            print("无效选择，使用默认服务器")
            choice = "1"
        
        server_path = os.path.join(os.path.dirname(__file__), server_map[choice])
        print(f"连接到服务器: {server_path}")
        
        await client.connect_to_server(server_path)
        print("✓ 连接成功，开始交互式调试")
        
        while True:
            try:
                query = input("\n调试查询: ").strip()
                
                if query.lower() == 'quit':
                    break
                elif query.lower() == 'help':
                    print("""
调试命令:
- quit: 退出调试
- help: 显示此帮助
- tools: 列出可用工具
- 其他: 作为查询发送给AI
                    """)
                    continue
                elif query.lower() == 'tools':
                    response = await client.session.list_tools()
                    print("可用工具:")
                    for tool in response.tools:
                        print(f"  - {tool.name}: {tool.description}")
                    continue
                
                if query:
                    print("处理中...")
                    response = await client.process_query(query)
                    print(f"\n响应:\n{response}")
                
            except KeyboardInterrupt:
                print("\n中断调试")
                break
            except Exception as e:
                print(f"查询处理错误: {e}")
                traceback.print_exc()
                
    except Exception as e:
        print(f"交互式调试失败: {e}")
        traceback.print_exc()
    finally:
        await client.cleanup()

async def main():
    """主调试函数"""
    print("MCP Client 调试工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        mode = sys.argv[1]
    else:
        print("\n选择调试模式:")
        print("1. 测试基本连接")
        print("2. 测试网页爬虫")
        print("3. 交互式调试")
        mode = input("选择模式 (1-3): ").strip()
    
    try:
        if mode == "1":
            await test_connection()
        elif mode == "2":
            await test_web_scraper()
        elif mode == "3":
            await interactive_debug()
        else:
            print("无效模式，运行所有测试")
            await test_connection()
            await test_web_scraper()
            
    except Exception as e:
        print(f"调试过程中发生错误: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main()) 