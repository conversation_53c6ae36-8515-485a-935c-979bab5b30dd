import os
from openai import OpenAI

try:
    client = OpenAI(
        api_key='sk-G1o4DYEttP9ab1HBNW8jPyE1Sq8jyel6CplOq0qxKmLnJMiE',
        base_url="https://api.aigc.bar/v1",
    )

    completion = client.chat.completions.create(
        model="claude-sonnet-4-20250514",
        messages=[
            {'role': 'system', 'content': 'You are a Java development assistant.'},
            {'role': 'user', 'content': '写一个spring service单元测试'}
            ]
    )
    print(f'-----------------------------------{completion}')
    print(completion.choices[0].message.content)
except Exception as e:
    print(f"错误信息：{e}")