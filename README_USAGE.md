# MCP Web Scraper Server - Usage Guide

## Overview

The MCP Web Scraper Server provides tools for web scraping and data processing through the Model Context Protocol (MCP). It's built using FastMCP for simplicity and includes tools for fetching web content, parsing HTML, and managing JSON data.

## Available Tools

### 1. `fetch_url(url: str, timeout: int = 10)`
Fetches content from a URL and returns metadata including:
- HTML content
- HTTP status code
- Response headers
- Final URL (after redirects)
- Encoding information

### 2. `parse_html(html_content: str, selectors: dict = None)`
Parses HTML content using BeautifulSoup and extracts:
- Page title
- All text content
- All links
- Custom data using CSS selectors (if provided)

### 3. `scrape_url(url: str, selectors: dict = None, timeout: int = 10)`
Combines fetch_url and parse_html in one step for convenience.

### 4. `save_json(data: any, filename: str)`
Saves any data to a JSON file with UTF-8 encoding.

### 5. `load_json(filename: str)`
Loads data from a JSON file.

## Running the Server

### Option 1: Direct Testing
```bash
# Test the server tools directly
python test_updated_server.py
```

### Option 2: Interactive Mode (requires Anthropic API key)
```bash
# Set your API key
export ANTHROPIC_API_KEY="your-api-key-here"

# Run interactive client
python client.py server.py
```

## Example Usage

### Basic Web Scraping
```python
# Fetch a webpage
result = await client.session.call_tool("fetch_url", {
    "url": "https://example.com"
})

# Parse HTML with custom selectors
result = await client.session.call_tool("parse_html", {
    "html_content": "<html>...</html>",
    "selectors": {
        "titles": "h1, h2",
        "links": "a",
        "paragraphs": "p"
    }
})

# Scrape a URL in one step
result = await client.session.call_tool("scrape_url", {
    "url": "https://example.com",
    "selectors": {
        "headlines": "h1",
        "content": "p"
    }
})
```

### Data Management
```python
# Save scraped data
await client.session.call_tool("save_json", {
    "data": {"title": "Example", "content": "..."},
    "filename": "scraped_data.json"
})

# Load saved data
result = await client.session.call_tool("load_json", {
    "filename": "scraped_data.json"
})
```

## Integration with Claude

When using with Claude (via Anthropic API), you can ask natural language questions like:

- "Scrape the headlines from https://example.com"
- "Extract all links from this webpage and save them to a JSON file"
- "Parse this HTML content and find all paragraph text"

## Files

- `server.py` - Main MCP server implementation
- `client.py` - MCP client for connecting to servers
- `test_updated_server.py` - Test script demonstrating all tools
- `fast_server.py` - Alternative simplified server example
- `people_news_crawler.py` - Original news crawler (separate from MCP)

## Notes

- The server uses a proper User-Agent header to avoid being blocked
- Encoding is automatically detected for better text handling
- All tools include error handling and return structured results
- The server supports both direct tool calls and Claude integration