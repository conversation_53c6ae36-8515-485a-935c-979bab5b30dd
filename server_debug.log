2025-06-27 11:18:22,698 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-27 11:18:22,824 - ServerDebugger - INFO - 尝试连接到服务器: /Users/<USER>/Downloads/MCP_TEST/mcp-client/server.py
2025-06-27 11:18:22,825 - ServerDebugger - INFO - 服务器文件存在，大小: 4669 bytes
2025-06-27 11:18:23,851 - ServerDebugger - INFO - ✓ 成功连接到服务器
2025-06-27 11:18:23,854 - ServerDebugger - INFO - 可用工具: ['fetch_url', 'parse_html', 'scrape_url', 'save_json', 'load_json']
2025-06-27 11:18:31,236 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-27 11:18:31,310 - ServerDebugger - INFO - 尝试连接到服务器: /Users/<USER>/Downloads/MCP_TEST/mcp-client/server.py
2025-06-27 11:18:31,310 - Server<PERSON><PERSON><PERSON>ger - INFO - 服务器文件存在，大小: 4669 bytes
2025-06-27 11:18:32,268 - ServerDebugger - INFO - ✓ 成功连接到服务器
2025-06-27 11:18:32,271 - ServerDebugger - INFO - 可用工具: ['fetch_url', 'parse_html', 'scrape_url', 'save_json', 'load_json']
2025-06-27 11:18:32,277 - anthropic._base_client - DEBUG - Request options: {'method': 'post', 'url': '/v1/messages', 'timeout': Timeout(connect=5.0, read=600, write=600, pool=600), 'files': None, 'idempotency_key': 'stainless-python-retry-60635cb3-c88c-4fd1-851a-ecf37020bdd8', 'json_data': {'max_tokens': 1000, 'messages': [{'role': 'user', 'content': '\n            Use the fetch_url tool to get content from "https://httpbin.org/json" \n            and show me the response status and content length.\n            '}], 'model': 'claude-3-5-sonnet-20241022', 'tools': [{'name': 'fetch_url', 'description': 'Fetch content from a URL and return the HTML content along with metadata', 'input_schema': {'properties': {'url': {'title': 'Url', 'type': 'string'}, 'timeout': {'default': 10, 'title': 'Timeout', 'type': 'integer'}}, 'required': ['url'], 'title': 'fetch_urlArguments', 'type': 'object'}}, {'name': 'parse_html', 'description': 'Parse HTML content and extract data using CSS selectors', 'input_schema': {'properties': {'html_content': {'title': 'Html Content', 'type': 'string'}, 'selectors': {'anyOf': [{'additionalProperties': {'type': 'string'}, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Selectors'}}, 'required': ['html_content'], 'title': 'parse_htmlArguments', 'type': 'object'}}, {'name': 'scrape_url', 'description': 'Fetch and parse a URL in one step, optionally with CSS selectors', 'input_schema': {'properties': {'url': {'title': 'Url', 'type': 'string'}, 'selectors': {'anyOf': [{'additionalProperties': {'type': 'string'}, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Selectors'}, 'timeout': {'default': 10, 'title': 'Timeout', 'type': 'integer'}}, 'required': ['url'], 'title': 'scrape_urlArguments', 'type': 'object'}}, {'name': 'save_json', 'description': 'Save data to a JSON file', 'input_schema': {'properties': {'data': {'title': 'Data'}, 'filename': {'title': 'Filename', 'type': 'string'}}, 'required': ['data', 'filename'], 'title': 'save_jsonArguments', 'type': 'object'}}, {'name': 'load_json', 'description': 'Load data from a JSON file', 'input_schema': {'properties': {'filename': {'title': 'Filename', 'type': 'string'}}, 'required': ['filename'], 'title': 'load_jsonArguments', 'type': 'object'}}]}}
2025-06-27 11:18:32,345 - anthropic._base_client - DEBUG - Sending HTTP Request: POST https://api.anthropic.com/v1/messages
2025-06-27 11:18:32,346 - httpcore.connection - DEBUG - connect_tcp.started host='api.anthropic.com' port=443 local_address=None timeout=5.0 socket_options=None
2025-06-27 11:18:32,388 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x1037d5550>
2025-06-27 11:18:32,388 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x10374c830> server_hostname='api.anthropic.com' timeout=5.0
2025-06-27 11:18:32,654 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x103789090>
2025-06-27 11:18:32,654 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-27 11:18:32,655 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-27 11:18:32,655 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-27 11:18:32,655 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-27 11:18:32,655 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-27 11:18:32,731 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 403, b'Forbidden', [(b'Date', b'Fri, 27 Jun 2025 03:18:32 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'none'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9561cc37397b0960-HKG'), (b'Content-Encoding', b'gzip')])
2025-06-27 11:18:32,733 - httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages "HTTP/1.1 403 Forbidden"
2025-06-27 11:18:32,739 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-27 11:18:32,740 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-27 11:18:32,740 - httpcore.http11 - DEBUG - response_closed.started
2025-06-27 11:18:32,740 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-27 11:18:32,741 - anthropic._base_client - DEBUG - HTTP Response: POST https://api.anthropic.com/v1/messages "403 Forbidden" Headers({'date': 'Fri, 27 Jun 2025 03:18:32 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Accept-Encoding', 'x-robots-tag': 'none', 'server': 'cloudflare', 'cf-ray': '9561cc37397b0960-HKG', 'content-encoding': 'gzip'})
2025-06-27 11:18:32,741 - anthropic._base_client - DEBUG - request_id: None
2025-06-27 11:18:32,741 - anthropic._base_client - DEBUG - Encountered httpx.HTTPStatusError
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/anthropic/_base_client.py", line 1082, in request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/httpx/_models.py", line 829, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '403 Forbidden' for url 'https://api.anthropic.com/v1/messages'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/403
2025-06-27 11:18:32,746 - anthropic._base_client - DEBUG - Not retrying
2025-06-27 11:18:32,746 - anthropic._base_client - DEBUG - Re-raising status error
2025-06-27 11:18:32,747 - ServerDebugger - ERROR - ✗ fetch_url 测试失败: Error code: 403 - {'error': {'type': 'forbidden', 'message': 'Request not allowed'}}
2025-06-27 11:18:32,753 - ServerDebugger - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/debug_server.py", line 77, in test_fetch_url_tool
    response = await self.client.process_query(query)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/client.py", line 68, in process_query
    response = self.anthropic.messages.create(
        model="claude-3-5-sonnet-20241022",
    ...<2 lines>...
        tools=available_tools
    )
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/anthropic/_utils/_utils.py", line 283, in wrapper
    return func(*args, **kwargs)
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/anthropic/resources/messages/messages.py", line 978, in create
    return self._post(
           ~~~~~~~~~~^
        "/v1/messages",
        ^^^^^^^^^^^^^^^
    ...<26 lines>...
        stream_cls=Stream[RawMessageStreamEvent],
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/anthropic/_base_client.py", line 1307, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/anthropic/_base_client.py", line 1102, in request
    raise self._make_status_error_from_response(err.response) from None
anthropic.PermissionDeniedError: Error code: 403 - {'error': {'type': 'forbidden', 'message': 'Request not allowed'}}

2025-06-27 11:18:32,760 - anthropic._base_client - DEBUG - Request options: {'method': 'post', 'url': '/v1/messages', 'timeout': Timeout(connect=5.0, read=600, write=600, pool=600), 'files': None, 'idempotency_key': 'stainless-python-retry-f1a93a94-a535-4e66-b332-0481ee6b2e5b', 'json_data': {'max_tokens': 1000, 'messages': [{'role': 'user', 'content': '\n            Use the scrape_url tool to fetch content from "https://httpbin.org/html" \n            and extract all paragraph text using CSS selector "p".\n            '}], 'model': 'claude-3-5-sonnet-20241022', 'tools': [{'name': 'fetch_url', 'description': 'Fetch content from a URL and return the HTML content along with metadata', 'input_schema': {'properties': {'url': {'title': 'Url', 'type': 'string'}, 'timeout': {'default': 10, 'title': 'Timeout', 'type': 'integer'}}, 'required': ['url'], 'title': 'fetch_urlArguments', 'type': 'object'}}, {'name': 'parse_html', 'description': 'Parse HTML content and extract data using CSS selectors', 'input_schema': {'properties': {'html_content': {'title': 'Html Content', 'type': 'string'}, 'selectors': {'anyOf': [{'additionalProperties': {'type': 'string'}, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Selectors'}}, 'required': ['html_content'], 'title': 'parse_htmlArguments', 'type': 'object'}}, {'name': 'scrape_url', 'description': 'Fetch and parse a URL in one step, optionally with CSS selectors', 'input_schema': {'properties': {'url': {'title': 'Url', 'type': 'string'}, 'selectors': {'anyOf': [{'additionalProperties': {'type': 'string'}, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Selectors'}, 'timeout': {'default': 10, 'title': 'Timeout', 'type': 'integer'}}, 'required': ['url'], 'title': 'scrape_urlArguments', 'type': 'object'}}, {'name': 'save_json', 'description': 'Save data to a JSON file', 'input_schema': {'properties': {'data': {'title': 'Data'}, 'filename': {'title': 'Filename', 'type': 'string'}}, 'required': ['data', 'filename'], 'title': 'save_jsonArguments', 'type': 'object'}}, {'name': 'load_json', 'description': 'Load data from a JSON file', 'input_schema': {'properties': {'filename': {'title': 'Filename', 'type': 'string'}}, 'required': ['filename'], 'title': 'load_jsonArguments', 'type': 'object'}}]}}
2025-06-27 11:18:32,761 - anthropic._base_client - DEBUG - Sending HTTP Request: POST https://api.anthropic.com/v1/messages
2025-06-27 11:18:32,762 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-27 11:18:32,764 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-27 11:18:32,764 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-27 11:18:32,765 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-27 11:18:32,765 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-27 11:18:32,903 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 403, b'Forbidden', [(b'Date', b'Fri, 27 Jun 2025 03:18:33 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'none'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9561cc383b750960-HKG'), (b'Content-Encoding', b'gzip')])
2025-06-27 11:18:32,903 - httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages "HTTP/1.1 403 Forbidden"
2025-06-27 11:18:32,904 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-27 11:18:32,904 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-27 11:18:32,904 - httpcore.http11 - DEBUG - response_closed.started
2025-06-27 11:18:32,905 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-27 11:18:32,905 - anthropic._base_client - DEBUG - HTTP Response: POST https://api.anthropic.com/v1/messages "403 Forbidden" Headers({'date': 'Fri, 27 Jun 2025 03:18:33 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Accept-Encoding', 'x-robots-tag': 'none', 'server': 'cloudflare', 'cf-ray': '9561cc383b750960-HKG', 'content-encoding': 'gzip'})
2025-06-27 11:18:32,905 - anthropic._base_client - DEBUG - request_id: None
2025-06-27 11:18:32,905 - anthropic._base_client - DEBUG - Encountered httpx.HTTPStatusError
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/anthropic/_base_client.py", line 1082, in request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/httpx/_models.py", line 829, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '403 Forbidden' for url 'https://api.anthropic.com/v1/messages'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/403
2025-06-27 11:18:32,906 - anthropic._base_client - DEBUG - Not retrying
2025-06-27 11:18:32,906 - anthropic._base_client - DEBUG - Re-raising status error
2025-06-27 11:18:32,906 - ServerDebugger - ERROR - ✗ parse_html 测试失败: Error code: 403 - {'error': {'type': 'forbidden', 'message': 'Request not allowed'}}
2025-06-27 11:18:32,908 - ServerDebugger - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/debug_server.py", line 100, in test_parse_html_tool
    response = await self.client.process_query(query)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/client.py", line 68, in process_query
    response = self.anthropic.messages.create(
        model="claude-3-5-sonnet-20241022",
    ...<2 lines>...
        tools=available_tools
    )
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/anthropic/_utils/_utils.py", line 283, in wrapper
    return func(*args, **kwargs)
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/anthropic/resources/messages/messages.py", line 978, in create
    return self._post(
           ~~~~~~~~~~^
        "/v1/messages",
        ^^^^^^^^^^^^^^^
    ...<26 lines>...
        stream_cls=Stream[RawMessageStreamEvent],
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/anthropic/_base_client.py", line 1307, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/anthropic/_base_client.py", line 1102, in request
    raise self._make_status_error_from_response(err.response) from None
anthropic.PermissionDeniedError: Error code: 403 - {'error': {'type': 'forbidden', 'message': 'Request not allowed'}}

2025-06-27 11:18:32,912 - anthropic._base_client - DEBUG - Request options: {'method': 'post', 'url': '/v1/messages', 'timeout': Timeout(connect=5.0, read=600, write=600, pool=600), 'files': None, 'idempotency_key': 'stainless-python-retry-d365b322-4c61-4b35-b2a0-885c8d4e3161', 'json_data': {'max_tokens': 1000, 'messages': [{'role': 'user', 'content': '\n            Use the save_json tool to save this data to "debug_test.json":\n            {"test": "data", "numbers": [1, 2, 3], "timestamp": "2024-01-01"}\n            '}], 'model': 'claude-3-5-sonnet-20241022', 'tools': [{'name': 'fetch_url', 'description': 'Fetch content from a URL and return the HTML content along with metadata', 'input_schema': {'properties': {'url': {'title': 'Url', 'type': 'string'}, 'timeout': {'default': 10, 'title': 'Timeout', 'type': 'integer'}}, 'required': ['url'], 'title': 'fetch_urlArguments', 'type': 'object'}}, {'name': 'parse_html', 'description': 'Parse HTML content and extract data using CSS selectors', 'input_schema': {'properties': {'html_content': {'title': 'Html Content', 'type': 'string'}, 'selectors': {'anyOf': [{'additionalProperties': {'type': 'string'}, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Selectors'}}, 'required': ['html_content'], 'title': 'parse_htmlArguments', 'type': 'object'}}, {'name': 'scrape_url', 'description': 'Fetch and parse a URL in one step, optionally with CSS selectors', 'input_schema': {'properties': {'url': {'title': 'Url', 'type': 'string'}, 'selectors': {'anyOf': [{'additionalProperties': {'type': 'string'}, 'type': 'object'}, {'type': 'null'}], 'default': None, 'title': 'Selectors'}, 'timeout': {'default': 10, 'title': 'Timeout', 'type': 'integer'}}, 'required': ['url'], 'title': 'scrape_urlArguments', 'type': 'object'}}, {'name': 'save_json', 'description': 'Save data to a JSON file', 'input_schema': {'properties': {'data': {'title': 'Data'}, 'filename': {'title': 'Filename', 'type': 'string'}}, 'required': ['data', 'filename'], 'title': 'save_jsonArguments', 'type': 'object'}}, {'name': 'load_json', 'description': 'Load data from a JSON file', 'input_schema': {'properties': {'filename': {'title': 'Filename', 'type': 'string'}}, 'required': ['filename'], 'title': 'load_jsonArguments', 'type': 'object'}}]}}
2025-06-27 11:18:32,914 - anthropic._base_client - DEBUG - Sending HTTP Request: POST https://api.anthropic.com/v1/messages
2025-06-27 11:18:32,915 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-27 11:18:32,915 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-27 11:18:32,915 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-27 11:18:32,915 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-27 11:18:32,915 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-27 11:18:33,034 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 403, b'Forbidden', [(b'Date', b'Fri, 27 Jun 2025 03:18:33 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'none'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9561cc391da70960-HKG'), (b'Content-Encoding', b'gzip')])
2025-06-27 11:18:33,034 - httpx - INFO - HTTP Request: POST https://api.anthropic.com/v1/messages "HTTP/1.1 403 Forbidden"
2025-06-27 11:18:33,035 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-27 11:18:33,035 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-27 11:18:33,035 - httpcore.http11 - DEBUG - response_closed.started
2025-06-27 11:18:33,035 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-27 11:18:33,035 - anthropic._base_client - DEBUG - HTTP Response: POST https://api.anthropic.com/v1/messages "403 Forbidden" Headers({'date': 'Fri, 27 Jun 2025 03:18:33 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'vary': 'Accept-Encoding', 'x-robots-tag': 'none', 'server': 'cloudflare', 'cf-ray': '9561cc391da70960-HKG', 'content-encoding': 'gzip'})
2025-06-27 11:18:33,036 - anthropic._base_client - DEBUG - request_id: None
2025-06-27 11:18:33,036 - anthropic._base_client - DEBUG - Encountered httpx.HTTPStatusError
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/anthropic/_base_client.py", line 1082, in request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/httpx/_models.py", line 829, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '403 Forbidden' for url 'https://api.anthropic.com/v1/messages'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/403
2025-06-27 11:18:33,037 - anthropic._base_client - DEBUG - Not retrying
2025-06-27 11:18:33,037 - anthropic._base_client - DEBUG - Re-raising status error
2025-06-27 11:18:33,037 - ServerDebugger - ERROR - ✗ JSON 操作测试失败: Error code: 403 - {'error': {'type': 'forbidden', 'message': 'Request not allowed'}}
2025-06-27 11:18:33,039 - ServerDebugger - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/debug_server.py", line 123, in test_json_operations
    response1 = await self.client.process_query(save_query)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/client.py", line 68, in process_query
    response = self.anthropic.messages.create(
        model="claude-3-5-sonnet-20241022",
    ...<2 lines>...
        tools=available_tools
    )
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/anthropic/_utils/_utils.py", line 283, in wrapper
    return func(*args, **kwargs)
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/anthropic/resources/messages/messages.py", line 978, in create
    return self._post(
           ~~~~~~~~~~^
        "/v1/messages",
        ^^^^^^^^^^^^^^^
    ...<26 lines>...
        stream_cls=Stream[RawMessageStreamEvent],
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/anthropic/_base_client.py", line 1307, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/anthropic/_base_client.py", line 1102, in request
    raise self._make_status_error_from_response(err.response) from None
anthropic.PermissionDeniedError: Error code: 403 - {'error': {'type': 'forbidden', 'message': 'Request not allowed'}}

2025-06-27 11:18:33,080 - httpcore.connection - DEBUG - close.started
2025-06-27 11:18:33,081 - httpcore.connection - DEBUG - close.complete
