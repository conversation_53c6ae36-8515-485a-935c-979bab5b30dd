#!/usr/bin/env python3
"""
快速调试 server.py 的脚本
用于快速检查服务器状态和基本功能
"""

import asyncio
import sys
import os
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from client import MCPClient

async def quick_check():
    """快速检查服务器状态"""
    print("🔍 快速检查 server.py 状态...")
    
    client = MCPClient()
    
    try:
        # 1. 检查文件
        server_path = os.path.join(os.path.dirname(__file__), "server.py")
        if not os.path.exists(server_path):
            print("❌ 服务器文件不存在")
            return False
        
        print(f"✅ 服务器文件存在 ({os.path.getsize(server_path)} bytes)")
        
        # 2. 测试连接
        print("🔗 测试连接...")
        start_time = time.time()
        await client.connect_to_server(server_path)
        connect_time = time.time() - start_time
        
        print(f"✅ 连接成功 (耗时: {connect_time:.2f}秒)")
        
        # 3. 检查工具
        response = await client.session.list_tools()
        tools = [tool.name for tool in response.tools]
        print(f"✅ 可用工具: {', '.join(tools)}")
        
        # 4. 快速功能测试
        print("🧪 快速功能测试...")
        
        # 测试 fetch_url
        try:
            result = await client.session.call_tool("fetch_url", {"url": "https://httpbin.org/json"})
            if result.content and len(result.content) > 0:
                print("✅ fetch_url 工具正常")
            else:
                print("⚠️  fetch_url 工具返回空结果")
        except Exception as e:
            print(f"❌ fetch_url 工具失败: {e}")
        
        # 测试 save_json
        try:
            test_data = {"test": "quick_debug", "timestamp": time.time()}
            result = await client.session.call_tool("save_json", {
                "data": test_data, 
                "filename": "quick_test.json"
            })
            print("✅ save_json 工具正常")
        except Exception as e:
            print(f"❌ save_json 工具失败: {e}")
        
        print("\n🎉 快速检查完成！服务器状态良好。")
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False
    finally:
        await client.cleanup()

async def main():
    """主函数"""
    success = await quick_check()
    
    if success:
        print("\n💡 提示:")
        print("   - 运行 'python test_server_direct.py' 进行完整测试")
        print("   - 运行 'python debug_server.py 2' 进行交互式调试")
        print("   - 运行 'python client.py server.py' 启动客户端")
    else:
        print("\n🔧 建议:")
        print("   - 检查依赖是否安装: uv sync")
        print("   - 检查文件权限: chmod +x server.py")
        print("   - 查看详细日志: tail -f server_debug.log")

if __name__ == "__main__":
    asyncio.run(main()) 