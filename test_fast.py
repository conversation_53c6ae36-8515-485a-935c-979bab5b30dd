#!/usr/bin/env python3
import asyncio
import sys
import os

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from client import MCPClient

async def test_fast_server():
    """Test the FastMCP server"""
    client = MCPClient()
    
    try:
        server_path = os.path.join(os.path.dirname(__file__), "fast_server.py")
        print(f"Connecting to FastMCP server: {server_path}")
        await client.connect_to_server(server_path)
        
        # Test 1: Echo
        print("\n=== Test 1: Echo ===")
        query1 = 'Use the echo tool with message "Hello FastMCP!"'
        response1 = await client.process_query(query1)
        print("Response:", response1)
        
        # Test 2: Add numbers
        print("\n=== Test 2: Add numbers ===")
        query2 = 'Use the add_numbers tool to add 10 and 25'
        response2 = await client.process_query(query2)
        print("Response:", response2)
        
        # Test 3: Fetch webpage
        print("\n=== Test 3: Fetch webpage ===")
        query3 = 'Use the fetch_webpage tool to get information about "https://httpbin.org/html"'
        response3 = await client.process_query(query3)
        print("Response:", response3)
        
        print("\n=== FastMCP tests completed! ===")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await client.cleanup()

if __name__ == "__main__":
    asyncio.run(test_fast_server())