#!/usr/bin/env python3
import json
import logging
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin, urlparse
import requests
from bs4 import BeautifulSoup
import os
import time

from mcp.server.fastmcp import FastMCP

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("web-scraper-server")

# Initialize the FastMCP server
mcp = FastMCP("Web Scraper Server")

# Initialize session for web requests
session = requests.Session()
session.headers.update({
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
})

@mcp.tool()
def fetch_url(url: str, timeout: int = 10) -> Dict[str, Any]:
    """Fetch content from a URL and return the HTML content along with metadata"""
    try:
        response = session.get(url, timeout=timeout)
        response.raise_for_status()
        
        # Try to detect encoding
        if response.encoding == 'ISO-8859-1':
            response.encoding = response.apparent_encoding or 'utf-8'
        
        return {
            'success': True,
            'status_code': response.status_code,
            'content': response.text,
            'headers': dict(response.headers),
            'url': response.url,
            'encoding': response.encoding
        }
    except requests.RequestException as e:
        return {
            'success': False,
            'error': str(e),
            'status_code': getattr(e.response, 'status_code', None) if hasattr(e, 'response') else None
        }

@mcp.tool()
def parse_html(html_content: str, selectors: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """Parse HTML content and extract data using CSS selectors"""
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        result = {
            'success': True,
            'title': soup.title.string.strip() if soup.title else '',
            'text_content': soup.get_text().strip()
        }
        
        if selectors:
            extracted_data = {}
            for key, selector in selectors.items():
                elements = soup.select(selector)
                if elements:
                    extracted_data[key] = [elem.get_text().strip() for elem in elements]
                else:
                    extracted_data[key] = []
            result['extracted_data'] = extracted_data
        
        # Extract all links
        links = []
        for link in soup.find_all('a', href=True):
            links.append({
                'text': link.get_text().strip(),
                'href': link['href']
            })
        result['links'] = links
        
        return result
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

@mcp.tool()
def scrape_url(url: str, selectors: Optional[Dict[str, str]] = None, timeout: int = 10) -> Dict[str, Any]:
    """Fetch and parse a URL in one step, optionally with CSS selectors"""
    # Fetch the URL first
    fetch_result = fetch_url(url, timeout)
    if not fetch_result['success']:
        return fetch_result
    
    # Parse the HTML content
    parse_result = parse_html(fetch_result['content'], selectors)
    
    # Combine results
    return {
        'fetch_info': {
            'url': fetch_result['url'],
            'status_code': fetch_result['status_code'],
            'encoding': fetch_result['encoding']
        },
        'parsed_data': parse_result
    }

@mcp.tool()
def save_json(data: Any, filename: str) -> Dict[str, Any]:
    """Save data to a JSON file"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return {
            'success': True,
            'message': f'Data saved to {filename}',
            'file_size': os.path.getsize(filename)
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

@mcp.tool()
def load_json(filename: str) -> Dict[str, Any]:
    """Load data from a JSON file"""
    try:
        if not os.path.exists(filename):
            return {
                'success': False,
                'error': f'File {filename} does not exist'
            }
        
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return {
            'success': True,
            'data': data,
            'file_size': os.path.getsize(filename)
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

if __name__ == "__main__":
    mcp.run()