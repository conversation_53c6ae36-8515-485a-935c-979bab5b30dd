#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
人民网新闻爬虫
功能：爬取人民网首页新闻的标题和内容，并保存为JSON格式
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import random
import os

class PeopleNewsCrawler:
    def __init__(self):
        self.base_url = "http://www.people.com.cn/"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        }
        self.news_list = []
    
    def get_page(self, url):
        """获取网页内容"""
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            response.encoding = 'utf-8'  # 人民网使用utf-8编码
            if response.status_code == 200:
                return response.text
            else:
                print(f"请求失败，状态码：{response.status_code}")
                return None
        except Exception as e:
            print(f"请求异常：{e}")
            return None
    
    def parse_homepage(self):
        """解析首页，获取新闻链接"""
        html = self.get_page(self.base_url)
        if not html:
            return []
        
        soup = BeautifulSoup(html, 'html.parser')
        news_links = []
        
        # 获取首页新闻链接
        # 人民网首页的新闻链接通常在<a>标签中，并且有特定的类或结构
        # 这里我们尝试获取几种常见的新闻链接
        
        # 头条新闻
        top_news = soup.select('.headnews a')
        for news in top_news:
            if news.get('href') and news.get('href').startswith('http'):
                news_links.append({
                    'title': news.text.strip(),
                    'url': news.get('href')
                })
        
        # 重要新闻
        important_news = soup.select('.important-news-list a')
        for news in important_news:
            if news.get('href') and news.get('href').startswith('http'):
                news_links.append({
                    'title': news.text.strip(),
                    'url': news.get('href')
                })
        
        # 其他新闻区块
        other_news = soup.select('.news-box a')
        for news in other_news:
            if news.get('href') and news.get('href').startswith('http'):
                news_links.append({
                    'title': news.text.strip(),
                    'url': news.get('href')
                })
        
        # 如果上面的选择器没有找到足够的新闻，尝试更通用的方法
        if len(news_links) < 5:
            all_links = soup.find_all('a')
            for link in all_links:
                href = link.get('href', '')
                # 过滤出可能是新闻的链接
                if href and href.startswith('http') and ('content_' in href or 'article' in href or 'news' in href):
                    title = link.text.strip()
                    if title and len(title) > 5:  # 标题通常不会太短
                        news_links.append({
                            'title': title,
                            'url': href
                        })
        
        # 去重
        unique_links = []
        seen_urls = set()
        for news in news_links:
            if news['url'] not in seen_urls and news['title']:
                seen_urls.add(news['url'])
                unique_links.append(news)
        
        return unique_links[:10]  # 限制为前10条新闻
    
    def parse_news_content(self, news_info):
        """解析新闻内容页面"""
        url = news_info['url']
        html = self.get_page(url)
        if not html:
            return None
        
        soup = BeautifulSoup(html, 'html.parser')
        
        # 尝试不同的选择器来获取新闻内容
        # 人民网的新闻内容通常在特定的div中
        content = ""
        
        # 尝试获取标题
        title_element = soup.select_one('.article-title') or soup.select_one('.text_title') or soup.select_one('h1')
        title = title_element.text.strip() if title_element else news_info['title']
        
        # 尝试获取内容
        content_element = soup.select_one('#rwb_zw') or soup.select_one('.box_con') or soup.select_one('.content')
        if content_element:
            # 获取所有段落
            paragraphs = content_element.select('p')
            if paragraphs:
                content = '\n'.join([p.text.strip() for p in paragraphs])
            else:
                content = content_element.text.strip()
        
        # 如果没有找到内容，尝试更通用的方法
        if not content:
            # 尝试找到所有可能包含文章内容的段落
            all_paragraphs = soup.select('p')
            # 过滤掉太短的段落
            valid_paragraphs = [p.text.strip() for p in all_paragraphs if len(p.text.strip()) > 20]
            if valid_paragraphs:
                content = '\n'.join(valid_paragraphs)
        
        return {
            'title': title,
            'url': url,
            'content': content,
            'crawl_time': time.strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def crawl(self):
        """爬取新闻"""
        print("开始爬取人民网首页新闻...")
        news_links = self.parse_homepage()
        print(f"找到 {len(news_links)} 条新闻链接")
        
        for i, news_info in enumerate(news_links):
            print(f"正在爬取第 {i+1}/{len(news_links)} 条新闻：{news_info['title']}")
            news_data = self.parse_news_content(news_info)
            if news_data and news_data['content']:
                self.news_list.append(news_data)
            
            # 随机暂停，避免请求过于频繁
            time.sleep(random.uniform(1, 3))
        
        print(f"成功爬取 {len(self.news_list)} 条新闻")
    
    def save_to_json(self, filename="people_news.json"):
        """保存为JSON文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.news_list, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到 {filename}")

def main():
    crawler = PeopleNewsCrawler()
    crawler.crawl()
    crawler.save_to_json()
    print("爬虫任务完成！")

if __name__ == "__main__":
    main()
