#!/usr/bin/env python3
"""
直接测试 server.py 功能的脚本
不依赖 AI 模型，直接调用服务器工具
"""

import asyncio
import sys
import os
import json
import time
from typing import Dict, Any

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from contextlib import AsyncExitStack

class DirectServerTester:
    """直接服务器测试器"""
    
    def __init__(self):
        self.session: ClientSession = None
        self.exit_stack = AsyncExitStack()
        self.server_path = os.path.join(os.path.dirname(__file__), "server.py")
    
    async def connect_to_server(self):
        """连接到服务器"""
        print(f"连接到服务器: {self.server_path}")
        
        if not os.path.exists(self.server_path):
            raise FileNotFoundError(f"服务器文件不存在: {self.server_path}")
        
        server_params = StdioServerParameters(
            command="python",
            args=[self.server_path],
            env=None
        )
        
        stdio_transport = await self.exit_stack.enter_async_context(stdio_client(server_params))
        self.stdio, self.write = stdio_transport
        self.session = await self.exit_stack.enter_async_context(ClientSession(self.stdio, self.write))
        
        await self.session.initialize()
        
        # 获取可用工具
        response = await self.session.list_tools()
        tools = [tool.name for tool in response.tools]
        print(f"✓ 连接成功，可用工具: {tools}")
        
        return tools
    
    async def test_fetch_url(self):
        """测试 fetch_url 工具"""
        print("\n=== 测试 fetch_url 工具 ===")
        
        try:
            # 测试有效 URL
            result = await self.session.call_tool("fetch_url", {
                "url": "https://httpbin.org/json",
                "timeout": 10
            })
            
            print("✓ fetch_url 测试成功")
            print(f"状态码: {json.loads(result.content[0].text).get('status_code')}")
            print(f"内容长度: {len(json.loads(result.content[0].text).get('content', ''))}")
            
            return True
            
        except Exception as e:
            print(f"✗ fetch_url 测试失败: {e}")
            return False
    
    async def test_parse_html(self):
        """测试 parse_html 工具"""
        print("\n=== 测试 parse_html 工具 ===")
        
        try:
            # 先获取 HTML 内容
            fetch_result = await self.session.call_tool("fetch_url", {
                "url": "https://httpbin.org/html",
                "timeout": 10
            })
            
            fetch_data = json.loads(fetch_result.content[0].text)
            html_content = fetch_data.get('content', '')
            
            # 解析 HTML
            parse_result = await self.session.call_tool("parse_html", {
                "html_content": html_content,
                "selectors": {
                    "paragraphs": "p",
                    "title": "title"
                }
            })
            
            print("✓ parse_html 测试成功")
            parse_data = json.loads(parse_result.content[0].text)
            print(f"标题: {parse_data.get('title', 'N/A')}")
            print(f"提取的段落数: {len(parse_data.get('extracted_data', {}).get('paragraphs', []))}")
            
            return True
            
        except Exception as e:
            print(f"✗ parse_html 测试失败: {e}")
            return False
    
    async def test_scrape_url(self):
        """测试 scrape_url 工具"""
        print("\n=== 测试 scrape_url 工具 ===")
        
        try:
            result = await self.session.call_tool("scrape_url", {
                "url": "https://httpbin.org/html",
                "selectors": {
                    "paragraphs": "p",
                    "title": "title"
                },
                "timeout": 10
            })
            
            print("✓ scrape_url 测试成功")
            data = json.loads(result.content[0].text)
            print(f"获取信息: {data.get('fetch_info', {}).get('status_code')}")
            print(f"解析标题: {data.get('parsed_data', {}).get('title', 'N/A')}")
            
            return True
            
        except Exception as e:
            print(f"✗ scrape_url 测试失败: {e}")
            return False
    
    async def test_json_operations(self):
        """测试 JSON 文件操作"""
        print("\n=== 测试 JSON 文件操作 ===")
        
        try:
            # 测试数据
            test_data = {
                "message": "Hello from direct test",
                "timestamp": "2024-01-01",
                "numbers": [1, 2, 3, 4, 5]
            }
            
            # 保存 JSON
            save_result = await self.session.call_tool("save_json", {
                "data": test_data,
                "filename": "direct_test.json"
            })
            
            print("✓ save_json 测试成功")
            save_data = json.loads(save_result.content[0].text)
            print(f"保存结果: {save_data.get('message')}")
            
            # 加载 JSON
            load_result = await self.session.call_tool("load_json", {
                "filename": "direct_test.json"
            })
            
            print("✓ load_json 测试成功")
            load_data = json.loads(load_result.content[0].text)
            loaded_content = load_data.get('data', {})
            print(f"加载的数据: {loaded_content.get('message')}")
            
            # 验证数据一致性
            if loaded_content == test_data:
                print("✓ 数据一致性验证通过")
            else:
                print("✗ 数据一致性验证失败")
                return False
            
            return True
            
        except Exception as e:
            print(f"✗ JSON 操作测试失败: {e}")
            return False
    
    async def test_error_handling(self):
        """测试错误处理"""
        print("\n=== 测试错误处理 ===")
        
        try:
            # 测试无效 URL
            invalid_result = await self.session.call_tool("fetch_url", {
                "url": "https://invalid-url-that-does-not-exist.com",
                "timeout": 5
            })
            
            invalid_data = json.loads(invalid_result.content[0].text)
            if not invalid_data.get('success', True):
                print("✓ 无效 URL 错误处理正确")
            else:
                print("✗ 无效 URL 应该返回错误")
                return False
            
            # 测试无效文件
            file_result = await self.session.call_tool("load_json", {
                "filename": "non-existent-file.json"
            })
            
            file_data = json.loads(file_result.content[0].text)
            if not file_data.get('success', True):
                print("✓ 无效文件错误处理正确")
            else:
                print("✗ 无效文件应该返回错误")
                return False
            
            return True
            
        except Exception as e:
            print(f"✗ 错误处理测试失败: {e}")
            return False
    
    async def test_performance(self):
        """测试性能"""
        print("\n=== 测试性能 ===")
        
        urls = [
            "https://httpbin.org/json",
            "https://httpbin.org/html",
            "https://httpbin.org/xml"
        ]
        
        total_time = 0
        success_count = 0
        
        for i, url in enumerate(urls):
            try:
                start_time = time.time()
                result = await self.session.call_tool("fetch_url", {
                    "url": url,
                    "timeout": 10
                })
                end_time = time.time()
                
                request_time = end_time - start_time
                total_time += request_time
                success_count += 1
                
                data = json.loads(result.content[0].text)
                print(f"请求 {i+1} ({url}): {request_time:.2f}秒, 状态码: {data.get('status_code')}")
                
            except Exception as e:
                print(f"请求 {i+1} ({url}): 失败 - {e}")
        
        if success_count > 0:
            avg_time = total_time / success_count
            print(f"\n平均请求时间: {avg_time:.2f}秒")
            print(f"成功率: {success_count}/{len(urls)} ({success_count/len(urls)*100:.1f}%)")
            return True
        else:
            print("所有请求都失败了")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("Server.py 直接功能测试")
        print("=" * 50)
        
        try:
            # 连接服务器
            tools = await self.connect_to_server()
            
            # 运行测试
            tests = [
                ("fetch_url 测试", self.test_fetch_url),
                ("parse_html 测试", self.test_parse_html),
                ("scrape_url 测试", self.test_scrape_url),
                ("JSON 操作测试", self.test_json_operations),
                ("错误处理测试", self.test_error_handling),
                ("性能测试", self.test_performance),
            ]
            
            results = []
            for test_name, test_func in tests:
                print(f"\n运行 {test_name}...")
                try:
                    result = await test_func()
                    results.append((test_name, result))
                    print(f"{test_name}: {'✓ 通过' if result else '✗ 失败'}")
                except Exception as e:
                    results.append((test_name, False))
                    print(f"{test_name}: ✗ 异常 - {e}")
            
            # 总结结果
            print("\n=== 测试总结 ===")
            passed = sum(1 for _, result in results if result)
            total = len(results)
            
            for test_name, result in results:
                status = "✓ 通过" if result else "✗ 失败"
                print(f"{test_name}: {status}")
            
            print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
            
            return passed == total
            
        except Exception as e:
            print(f"测试过程中发生错误: {e}")
            return False
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        if self.exit_stack:
            await self.exit_stack.aclose()

async def main():
    """主函数"""
    tester = DirectServerTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过！服务器功能正常。")
    else:
        print("\n❌ 部分测试失败，请检查服务器实现。")

if __name__ == "__main__":
    asyncio.run(main()) 