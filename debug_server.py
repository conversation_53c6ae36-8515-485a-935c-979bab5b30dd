#!/usr/bin/env python3
"""
调试 server.py 的专用脚本
包含详细的测试、错误处理和性能监控
"""

import asyncio
import sys
import os
import logging
import traceback
import time
import json
from typing import Optional, Dict, Any

# 设置详细的日志记录
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('server_debug.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from client import MCPClient

class ServerDebugger:
    """服务器调试器"""
    
    def __init__(self):
        self.logger = logging.getLogger('ServerDebugger')
        self.client = MCPClient()
        self.server_path = os.path.join(os.path.dirname(__file__), "server.py")
        
    async def test_server_connection(self):
        """测试服务器连接"""
        print("=== 测试服务器连接 ===")
        try:
            self.logger.info(f"尝试连接到服务器: {self.server_path}")
            
            # 检查服务器文件
            if not os.path.exists(self.server_path):
                raise FileNotFoundError(f"服务器文件不存在: {self.server_path}")
            
            self.logger.info(f"服务器文件存在，大小: {os.path.getsize(self.server_path)} bytes")
            
            # 连接服务器
            await self.client.connect_to_server(self.server_path)
            self.logger.info("✓ 成功连接到服务器")
            
            # 获取可用工具
            response = await self.client.session.list_tools()
            tools = [tool.name for tool in response.tools]
            self.logger.info(f"可用工具: {tools}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"✗ 连接失败: {e}")
            self.logger.error(traceback.format_exc())
            return False
    
    async def test_fetch_url_tool(self):
        """测试 fetch_url 工具"""
        print("\n=== 测试 fetch_url 工具 ===")
        try:
            query = """
            Use the fetch_url tool to get content from "https://httpbin.org/json" 
            and show me the response status and content length.
            """
            
            start_time = time.time()
            response = await self.client.process_query(query)
            end_time = time.time()
            
            self.logger.info(f"请求耗时: {end_time - start_time:.2f}秒")
            print(f"响应: {response}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"✗ fetch_url 测试失败: {e}")
            self.logger.error(traceback.format_exc())
            return False
    
    async def test_parse_html_tool(self):
        """测试 parse_html 工具"""
        print("\n=== 测试 parse_html 工具 ===")
        try:
            query = """
            Use the scrape_url tool to fetch content from "https://httpbin.org/html" 
            and extract all paragraph text using CSS selector "p".
            """
            
            start_time = time.time()
            response = await self.client.process_query(query)
            end_time = time.time()
            
            self.logger.info(f"解析耗时: {end_time - start_time:.2f}秒")
            print(f"响应: {response}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"✗ parse_html 测试失败: {e}")
            self.logger.error(traceback.format_exc())
            return False
    
    async def test_json_operations(self):
        """测试 JSON 文件操作"""
        print("\n=== 测试 JSON 文件操作 ===")
        try:
            # 测试保存 JSON
            save_query = """
            Use the save_json tool to save this data to "debug_test.json":
            {"test": "data", "numbers": [1, 2, 3], "timestamp": "2024-01-01"}
            """
            
            response1 = await self.client.process_query(save_query)
            print(f"保存响应: {response1}")
            
            # 测试加载 JSON
            load_query = """
            Use the load_json tool to load data from "debug_test.json" and show me the contents.
            """
            
            response2 = await self.client.process_query(load_query)
            print(f"加载响应: {response2}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"✗ JSON 操作测试失败: {e}")
            self.logger.error(traceback.format_exc())
            return False
    
    async def test_error_handling(self):
        """测试错误处理"""
        print("\n=== 测试错误处理 ===")
        try:
            # 测试无效 URL
            invalid_url_query = """
            Use the fetch_url tool to get content from "https://invalid-url-that-does-not-exist.com"
            """
            
            response = await self.client.process_query(invalid_url_query)
            print(f"无效URL响应: {response}")
            
            # 测试无效 JSON 文件
            invalid_json_query = """
            Use the load_json tool to load data from "non-existent-file.json"
            """
            
            response2 = await self.client.process_query(invalid_json_query)
            print(f"无效文件响应: {response2}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"✗ 错误处理测试失败: {e}")
            self.logger.error(traceback.format_exc())
            return False
    
    async def test_performance(self):
        """测试性能"""
        print("\n=== 测试性能 ===")
        try:
            urls = [
                "https://httpbin.org/json",
                "https://httpbin.org/html", 
                "https://httpbin.org/xml"
            ]
            
            total_time = 0
            success_count = 0
            
            for i, url in enumerate(urls):
                query = f"""
                Use the fetch_url tool to get content from "{url}" 
                and show me the response status.
                """
                
                start_time = time.time()
                try:
                    response = await self.client.process_query(query)
                    end_time = time.time()
                    request_time = end_time - start_time
                    total_time += request_time
                    success_count += 1
                    
                    self.logger.info(f"请求 {i+1} 耗时: {request_time:.2f}秒")
                    
                except Exception as e:
                    self.logger.error(f"请求 {i+1} 失败: {e}")
            
            avg_time = total_time / success_count if success_count > 0 else 0
            self.logger.info(f"平均请求时间: {avg_time:.2f}秒")
            self.logger.info(f"成功率: {success_count}/{len(urls)} ({success_count/len(urls)*100:.1f}%)")
            
            return True
            
        except Exception as e:
            self.logger.error(f"✗ 性能测试失败: {e}")
            self.logger.error(traceback.format_exc())
            return False
    
    async def interactive_test(self):
        """交互式测试"""
        print("\n=== 交互式测试模式 ===")
        print("输入 'quit' 退出，输入 'help' 查看帮助")
        
        try:
            while True:
                query = input("\n测试查询: ").strip()
                
                if query.lower() == 'quit':
                    break
                elif query.lower() == 'help':
                    print("""
可用命令:
- quit: 退出测试
- help: 显示此帮助
- tools: 列出可用工具
- test: 运行所有测试
- 其他: 作为查询发送给服务器
                    """)
                    continue
                elif query.lower() == 'tools':
                    response = await self.client.session.list_tools()
                    print("可用工具:")
                    for tool in response.tools:
                        print(f"  - {tool.name}: {tool.description}")
                    continue
                elif query.lower() == 'test':
                    await self.run_all_tests()
                    continue
                
                if query:
                    print("处理中...")
                    start_time = time.time()
                    response = await self.client.process_query(query)
                    end_time = time.time()
                    
                    print(f"耗时: {end_time - start_time:.2f}秒")
                    print(f"响应:\n{response}")
                
        except KeyboardInterrupt:
            print("\n中断测试")
        except Exception as e:
            self.logger.error(f"交互式测试错误: {e}")
            self.logger.error(traceback.format_exc())
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("\n=== 运行所有测试 ===")
        
        tests = [
            ("连接测试", self.test_server_connection),
            ("fetch_url 测试", self.test_fetch_url_tool),
            ("parse_html 测试", self.test_parse_html_tool),
            ("JSON 操作测试", self.test_json_operations),
            ("错误处理测试", self.test_error_handling),
            ("性能测试", self.test_performance),
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n运行 {test_name}...")
            try:
                result = await test_func()
                results.append((test_name, result))
                print(f"{test_name}: {'✓ 通过' if result else '✗ 失败'}")
            except Exception as e:
                results.append((test_name, False))
                print(f"{test_name}: ✗ 异常 - {e}")
        
        # 总结结果
        print("\n=== 测试总结 ===")
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        for test_name, result in results:
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{test_name}: {status}")
        
        print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
        
        return passed == total
    
    async def cleanup(self):
        """清理资源"""
        await self.client.cleanup()

async def main():
    """主函数"""
    print("Server.py 调试工具")
    print("=" * 50)
    
    debugger = ServerDebugger()
    
    try:
        if len(sys.argv) > 1:
            mode = sys.argv[1]
        else:
            print("\n选择调试模式:")
            print("1. 运行所有测试")
            print("2. 交互式测试")
            print("3. 连接测试")
            print("4. 工具测试")
            mode = input("选择模式 (1-4): ").strip()
        
        if mode == "1":
            await debugger.run_all_tests()
        elif mode == "2":
            # 先确保连接成功
            if await debugger.test_server_connection():
                await debugger.interactive_test()
            else:
                print("连接失败，无法进行交互式测试")
        elif mode == "3":
            await debugger.test_server_connection()
        elif mode == "4":
            if await debugger.test_server_connection():
                await debugger.test_fetch_url_tool()
                await debugger.test_parse_html_tool()
                await debugger.test_json_operations()
        else:
            print("无效模式，运行所有测试")
            await debugger.run_all_tests()
            
    except Exception as e:
        print(f"调试过程中发生错误: {e}")
        traceback.print_exc()
    finally:
        await debugger.cleanup()

if __name__ == "__main__":
    asyncio.run(main()) 