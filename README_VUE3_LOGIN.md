# Vue 3 登录页面

一个现代化的 Vue 3 登录页面，具有美观的 UI 设计和完整的功能实现。

## 功能特点

### 🎨 界面设计
- 现代化的渐变背景设计
- 毛玻璃效果和阴影
- 响应式布局，支持移动端
- 流畅的动画和过渡效果

### 🔐 登录功能
- 用户名和密码验证
- 密码显示/隐藏切换
- 记住我功能
- 表单验证和错误提示
- 登录状态管理
- 社交登录（微信、QQ）

### 🛡️ 安全特性
- 路由守卫保护
- 本地存储 token 管理
- 自动登录状态检查
- 安全的密码输入

### 📱 用户体验
- 加载状态指示
- 成功/错误消息提示
- 键盘导航支持
- 无障碍访问支持

## 项目结构

```
vue3-login-page/
├── index.html                 # 主 HTML 文件
├── package.json              # 项目配置
├── vite.config.js           # Vite 配置
├── login.html               # 单文件登录页面（可直接使用）
├── src/
│   ├── main.js              # 应用入口
│   ├── App.vue              # 主应用组件
│   ├── style.css            # 全局样式
│   ├── router/
│   │   └── index.js         # 路由配置
│   ├── stores/
│   │   └── auth.js          # 认证状态管理
│   └── views/
│       ├── Login.vue        # 登录页面组件
│       └── Home.vue         # 首页组件
└── README_VUE3_LOGIN.md     # 使用说明
```

## 快速开始

### 方法一：直接使用单文件版本

1. 直接打开 `login.html` 文件在浏览器中查看
2. 测试账号：`admin` / `123456`

### 方法二：完整项目开发

1. **安装依赖**
```bash
npm install
# 或
yarn install
```

2. **启动开发服务器**
```bash
npm run dev
# 或
yarn dev
```

3. **构建生产版本**
```bash
npm run build
# 或
yarn build
```

4. **预览生产版本**
```bash
npm run preview
# 或
yarn preview
```

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **Vite** - 快速的前端构建工具
- **Vue Router** - 官方路由管理器
- **Pinia** - 状态管理库
- **CSS3** - 现代 CSS 特性

## 主要组件

### Login.vue
登录页面的主要组件，包含：
- 表单验证逻辑
- 密码显示切换
- 消息提示系统
- 社交登录按钮

### Home.vue
登录成功后的首页，包含：
- 用户信息显示
- 功能卡片展示
- 退出登录功能

### auth.js (Pinia Store)
认证状态管理，包含：
- 用户登录/登出逻辑
- Token 管理
- 本地存储操作

## 自定义配置

### 修改登录验证
在 `src/stores/auth.js` 中修改登录逻辑：

```javascript
// 修改验证条件
if (credentials.username === 'your_username' && credentials.password === 'your_password') {
  // 登录成功逻辑
}
```

### 修改样式主题
在 `src/views/Login.vue` 中修改 CSS 变量：

```css
.login-page {
  background: linear-gradient(135deg, #your_color1 0%, #your_color2 100%);
}
```

### 添加新的社交登录
在 `Login.vue` 中添加新的社交登录按钮：

```vue
<button class="social-button new-platform" @click="socialLogin('new_platform')">
  <span class="icon">🔗</span>
  新平台登录
</button>
```

## API 集成

### 替换模拟 API
将 `src/stores/auth.js` 中的模拟 API 替换为真实接口：

```javascript
const login = async (credentials) => {
  try {
    const response = await fetch('/api/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials)
    });
    
    const data = await response.json();
    
    if (data.success) {
      // 处理登录成功
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    // 处理错误
  }
};
```

### 添加请求拦截器
在 `src/main.js` 中添加 axios 拦截器：

```javascript
import axios from 'axios';

// 请求拦截器
axios.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 响应拦截器
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response.status === 401) {
      // 处理未授权
      router.push('/login');
    }
    return Promise.reject(error);
  }
);
```

## 部署

### 静态部署
1. 运行 `npm run build`
2. 将 `dist` 目录部署到静态服务器

### Docker 部署
```dockerfile
FROM nginx:alpine
COPY dist /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 性能优化

- 使用 Vite 进行快速构建
- 组件懒加载
- 图片优化
- CSS 代码分割

## 安全建议

1. **HTTPS** - 生产环境必须使用 HTTPS
2. **Token 过期** - 实现 token 自动刷新
3. **XSS 防护** - 对用户输入进行过滤
4. **CSRF 防护** - 添加 CSRF token
5. **密码强度** - 实施密码强度要求

## 常见问题

### Q: 如何修改登录验证逻辑？
A: 在 `src/stores/auth.js` 的 `login` 方法中修改验证条件。

### Q: 如何添加新的路由？
A: 在 `src/router/index.js` 中添加新的路由配置。

### Q: 如何自定义样式？
A: 修改对应组件的 `<style>` 部分或全局样式文件。

### Q: 如何集成真实 API？
A: 替换 `auth.js` 中的模拟 API 调用为真实的 HTTP 请求。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 许可证

MIT License 