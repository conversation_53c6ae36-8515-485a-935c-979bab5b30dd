#!/usr/bin/env python3
import asyncio
import requests
from bs4 import <PERSON><PERSON><PERSON>p
import json
from typing import Dict, Any

from mcp.server.fastmcp import FastMCP

# Create FastMCP server
mcp = FastMCP("Web Scraper Server")

@mcp.tool()
def echo(message: str) -> str:
    """Echo back the input message"""
    return f"Echo: {message}"

@mcp.tool()
def add_numbers(a: float, b: float) -> float:
    """Add two numbers together"""
    return a + b

@mcp.tool()
def fetch_webpage(url: str) -> Dict[str, Any]:
    """Fetch a webpage and return basic information"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        return {
            "status_code": response.status_code,
            "title": soup.title.string if soup.title else "No title",
            "url": response.url,
            "text_length": len(soup.get_text().strip()),
            "links_count": len(soup.find_all('a', href=True))
        }
    except Exception as e:
        return {
            "error": str(e),
            "status_code": None
        }

@mcp.tool()
def parse_html_text(html_content: str) -> Dict[str, Any]:
    """Parse HTML content and extract text"""
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Extract text
        text = soup.get_text().strip()
        
        # Extract links
        links = []
        for link in soup.find_all('a', href=True):
            links.append({
                'text': link.get_text().strip(),
                'href': link['href']
            })
        
        return {
            "text": text,
            "links": links,
            "title": soup.title.string if soup.title else ""
        }
    except Exception as e:
        return {
            "error": str(e)
        }

if __name__ == "__main__":
    mcp.run()