#!/usr/bin/env python3
"""
Test script for the MCP server
Usage: python test_server.py
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from client import MCPClient

async def test_mcp_server():
    """Test the MCP server functionality"""
    client = MCPClient()
    
    try:
        # Connect to our server
        server_path = os.path.join(os.path.dirname(__file__), "server.py")
        print(f"Connecting to server: {server_path}")
        await client.connect_to_server(server_path)
        
        # Test 1: Scrape a simple webpage
        print("\n=== Test 1: Scraping a webpage ===")
        query1 = """
        Use the scrape_url tool to fetch content from "https://httpbin.org/html" and extract:
        - All paragraph text using CSS selector "p"
        - All links using CSS selector "a"
        """
        response1 = await client.process_query(query1)
        print("Response:", response1)
        
        # Test 2: Fetch URL and parse separately
        print("\n=== Test 2: Fetch and parse HTML separately ===")
        query2 = """
        First use fetch_url to get content from "https://httpbin.org/json", 
        then show me the result.
        """
        response2 = await client.process_query(query2)
        print("Response:", response2)
        
        # Test 3: Work with JSON files
        print("\n=== Test 3: JSON file operations ===")
        query3 = """
        Create some sample data and save it to "test_data.json" using save_json tool.
        The data should be: {"message": "Hello from MCP server", "timestamp": "2024-01-01", "items": [1, 2, 3]}
        """
        response3 = await client.process_query(query3)
        print("Response:", response3)
        
        # Test 4: Load the JSON file back
        print("\n=== Test 4: Load JSON file ===")
        query4 = """
        Load the data from "test_data.json" using the load_json tool and show me the contents.
        """
        response4 = await client.process_query(query4)
        print("Response:", response4)
        
        print("\n=== All tests completed! ===")
        
    except Exception as e:
        print(f"Error during testing: {e}")
    finally:
        await client.cleanup()

if __name__ == "__main__":
    print("Testing MCP Server...")
    asyncio.run(test_mcp_server())