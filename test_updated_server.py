#!/usr/bin/env python3
import asyncio
import sys
import os

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from client import MCPClient

async def test_updated_server():
    """Test the updated MCP server functionality"""
    client = MCPClient()
    
    try:
        # Connect to our updated server
        server_path = os.path.join(os.path.dirname(__file__), "server.py")
        print(f"Connecting to updated server: {server_path}")
        await client.connect_to_server(server_path)
        
        # Test 1: Fetch URL
        print("\n=== Test 1: Fetch URL ===")
        result1 = await client.session.call_tool("fetch_url", {"url": "https://httpbin.org/html"})
        print(f"Fetch result: {result1.content[0].text[:200]}...")
        
        # Test 2: Scrape URL with selectors
        print("\n=== Test 2: Scrape URL with selectors ===")
        result2 = await client.session.call_tool("scrape_url", {
            "url": "https://httpbin.org/html",
            "selectors": {"paragraphs": "p", "links": "a"}
        })
        print(f"Scrape result: {result2.content[0].text[:300]}...")
        
        # Test 3: Save JSON
        print("\n=== Test 3: Save JSON ===")
        test_data = {"message": "Hello from updated server", "items": [1, 2, 3]}
        result3 = await client.session.call_tool("save_json", {
            "data": test_data,
            "filename": "test_updated.json"
        })
        print(f"Save result: {result3.content[0].text}")
        
        # Test 4: Load JSON
        print("\n=== Test 4: Load JSON ===")
        result4 = await client.session.call_tool("load_json", {"filename": "test_updated.json"})
        print(f"Load result: {result4.content[0].text}")
        
        print("\n=== All updated server tests completed successfully! ===")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await client.cleanup()

if __name__ == "__main__":
    asyncio.run(test_updated_server())