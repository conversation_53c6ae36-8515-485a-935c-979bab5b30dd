#!/usr/bin/env python3
"""
Test script for the simple MCP server
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from client import MCPClient

async def test_simple_server():
    """Test the simple MCP server functionality"""
    client = MCPClient()
    
    try:
        # Connect to our simple server
        server_path = os.path.join(os.path.dirname(__file__), "simple_server.py")
        print(f"Connecting to server: {server_path}")
        await client.connect_to_server(server_path)
        
        # Test 1: Echo tool
        print("\n=== Test 1: Echo tool ===")
        query1 = 'Use the echo tool with message "Hello, MCP!"'
        response1 = await client.process_query(query1)
        print("Response:", response1)
        
        # Test 2: Add tool
        print("\n=== Test 2: Add tool ===")
        query2 = 'Use the add tool to add 5 and 3'
        response2 = await client.process_query(query2)
        print("Response:", response2)
        
        print("\n=== Simple tests completed! ===")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await client.cleanup()

if __name__ == "__main__":
    print("Testing Simple MCP Server...")
    asyncio.run(test_simple_server())