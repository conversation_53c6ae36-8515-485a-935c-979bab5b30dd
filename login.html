<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - Vue 3</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h1 {
            color: #333;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .login-header p {
            color: #666;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: #fff;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group input.error {
            border-color: #e74c3c;
            background: #fff5f5;
        }

        .form-group .error-message {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }

        .form-group .error-message.show {
            display: block;
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 40px;
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
            font-size: 18px;
            padding: 4px;
        }

        .password-toggle:hover {
            color: #333;
        }

        .remember-me {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .remember-me input[type="checkbox"] {
            width: auto;
            margin-right: 8px;
            accent-color: #667eea;
        }

        .remember-me label {
            margin-bottom: 0;
            font-size: 14px;
            color: #666;
        }

        .login-button {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .login-button:active {
            transform: translateY(0);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .login-button .spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        .login-button.loading .spinner {
            display: inline-block;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .forgot-password {
            text-align: center;
            margin-top: 20px;
        }

        .forgot-password a {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .forgot-password a:hover {
            color: #764ba2;
        }

        .divider {
            margin: 20px 0;
            text-align: center;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e1e5e9;
        }

        .divider span {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 15px;
            color: #666;
            font-size: 12px;
        }

        .social-login {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .social-button {
            flex: 1;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            background: white;
            color: #333;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .social-button:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .social-button.wechat {
            color: #07c160;
        }

        .social-button.qq {
            color: #12b7f5;
        }

        .signup-link {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            color: #666;
        }

        .signup-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .signup-link a:hover {
            color: #764ba2;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
            display: none;
        }

        .success-message.show {
            display: block;
        }

        .error-message-global {
            background: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
            display: none;
        }

        .error-message-global.show {
            display: block;
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 30px 20px;
                margin: 10px;
            }

            .login-header h1 {
                font-size: 24px;
            }

            .social-login {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="login-container">
            <div class="login-header">
                <h1>欢迎回来</h1>
                <p>请登录您的账户</p>
            </div>

            <!-- 成功消息 -->
            <div class="success-message" :class="{ show: showSuccessMessage }">
                {{ successMessage }}
            </div>

            <!-- 全局错误消息 -->
            <div class="error-message-global" :class="{ show: showGlobalError }">
                {{ globalErrorMessage }}
            </div>

            <form @submit.prevent="handleLogin">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input 
                        type="text" 
                        id="username" 
                        v-model="form.username" 
                        @blur="validateUsername"
                        :class="{ error: errors.username }"
                        placeholder="请输入用户名或邮箱"
                        autocomplete="username"
                    >
                    <div class="error-message" :class="{ show: errors.username }">
                        {{ errors.username }}
                    </div>
                </div>

                <div class="form-group">
                    <label for="password">密码</label>
                    <input 
                        :type="showPassword ? 'text' : 'password'" 
                        id="password" 
                        v-model="form.password" 
                        @blur="validatePassword"
                        :class="{ error: errors.password }"
                        placeholder="请输入密码"
                        autocomplete="current-password"
                    >
                    <button 
                        type="button" 
                        class="password-toggle" 
                        @click="togglePassword"
                        :title="showPassword ? '隐藏密码' : '显示密码'"
                    >
                        {{ showPassword ? '👁️' : '👁️‍🗨️' }}
                    </button>
                    <div class="error-message" :class="{ show: errors.password }">
                        {{ errors.password }}
                    </div>
                </div>

                <div class="remember-me">
                    <input 
                        type="checkbox" 
                        id="remember" 
                        v-model="form.remember"
                    >
                    <label for="remember">记住我</label>
                </div>

                <button 
                    type="submit" 
                    class="login-button" 
                    :class="{ loading: isLoading }"
                    :disabled="isLoading"
                >
                    <span class="spinner"></span>
                    {{ isLoading ? '登录中...' : '登录' }}
                </button>
            </form>

            <div class="forgot-password">
                <a href="#" @click.prevent="forgotPassword">忘记密码？</a>
            </div>

            <div class="divider">
                <span>或</span>
            </div>

            <div class="social-login">
                <button class="social-button wechat" @click="socialLogin('wechat')">
                    <span>💬</span>
                    微信登录
                </button>
                <button class="social-button qq" @click="socialLogin('qq')">
                    <span>🐧</span>
                    QQ登录
                </button>
            </div>

            <div class="signup-link">
                还没有账户？ <a href="#" @click.prevent="goToSignup">立即注册</a>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, reactive } = Vue;

        createApp({
            setup() {
                // 表单数据
                const form = reactive({
                    username: '',
                    password: '',
                    remember: false
                });

                // 状态管理
                const isLoading = ref(false);
                const showPassword = ref(false);
                const showSuccessMessage = ref(false);
                const showGlobalError = ref(false);
                const successMessage = ref('');
                const globalErrorMessage = ref('');

                // 错误信息
                const errors = reactive({
                    username: '',
                    password: ''
                });

                // 验证用户名
                const validateUsername = () => {
                    errors.username = '';
                    if (!form.username.trim()) {
                        errors.username = '请输入用户名';
                        return false;
                    }
                    if (form.username.length < 3) {
                        errors.username = '用户名至少需要3个字符';
                        return false;
                    }
                    return true;
                };

                // 验证密码
                const validatePassword = () => {
                    errors.password = '';
                    if (!form.password) {
                        errors.password = '请输入密码';
                        return false;
                    }
                    if (form.password.length < 6) {
                        errors.password = '密码至少需要6个字符';
                        return false;
                    }
                    return true;
                };

                // 切换密码显示
                const togglePassword = () => {
                    showPassword.value = !showPassword.value;
                };

                // 处理登录
                const handleLogin = async () => {
                    // 清除之前的消息
                    showSuccessMessage.value = false;
                    showGlobalError.value = false;

                    // 验证表单
                    const isUsernameValid = validateUsername();
                    const isPasswordValid = validatePassword();

                    if (!isUsernameValid || !isPasswordValid) {
                        return;
                    }

                    // 开始登录
                    isLoading.value = true;

                    try {
                        // 模拟API调用
                        await new Promise(resolve => setTimeout(resolve, 2000));

                        // 模拟登录成功
                        if (form.username === 'admin' && form.password === '123456') {
                            successMessage.value = '登录成功！正在跳转...';
                            showSuccessMessage.value = true;
                            
                            // 模拟跳转
                            setTimeout(() => {
                                alert('登录成功！在实际应用中这里会跳转到主页。');
                            }, 1500);
                        } else {
                            globalErrorMessage.value = '用户名或密码错误，请重试';
                            showGlobalError.value = true;
                        }
                    } catch (error) {
                        globalErrorMessage.value = '登录失败，请检查网络连接';
                        showGlobalError.value = true;
                    } finally {
                        isLoading.value = false;
                    }
                };

                // 忘记密码
                const forgotPassword = () => {
                    alert('忘记密码功能：在实际应用中这里会跳转到密码重置页面');
                };

                // 社交登录
                const socialLogin = (platform) => {
                    alert(`${platform === 'wechat' ? '微信' : 'QQ'}登录功能：在实际应用中这里会调用相应的OAuth接口`);
                };

                // 跳转到注册
                const goToSignup = () => {
                    alert('注册功能：在实际应用中这里会跳转到注册页面');
                };

                return {
                    form,
                    errors,
                    isLoading,
                    showPassword,
                    showSuccessMessage,
                    showGlobalError,
                    successMessage,
                    globalErrorMessage,
                    validateUsername,
                    validatePassword,
                    togglePassword,
                    handleLogin,
                    forgotPassword,
                    socialLogin,
                    goToSignup
                };
            }
        }).mount('#app');
    </script>
</body>
</html> 