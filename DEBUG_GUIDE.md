# client.py 调试指南

## 快速开始

### 1. 基本调试
```bash
# 运行调试脚本
python debug_client.py

# 或者直接运行交互式调试
python debug_client.py 3
```

### 2. 直接运行 client.py
```bash
# 连接到简单服务器
python client.py simple_server.py

# 连接到爬虫服务器
python client.py server.py
```

## 常见问题排查

### 1. 环境问题

**问题**: `ModuleNotFoundError: No module named 'mcp'`
**解决**: 安装依赖
```bash
pip install -r requirements.txt
# 或者
pip install mcp anthropic requests beautifulsoup4 python-dotenv
```

**问题**: `ModuleNotFoundError: No module named 'anthropic'`
**解决**: 安装 Anthropic 库
```bash
pip install anthropic
```

### 2. API 密钥问题

**问题**: `AuthenticationError` 或 `InvalidApiKey`
**解决**: 设置环境变量
```bash
# 创建 .env 文件
echo "ANTHROPIC_API_KEY=your_api_key_here" > .env

# 或者在终端中设置
export ANTHROPIC_API_KEY=your_api_key_here
```

### 3. 服务器连接问题

**问题**: `FileNotFoundError: 服务器文件不存在`
**解决**: 检查服务器文件路径
```bash
# 检查文件是否存在
ls -la *.py

# 确保在正确的目录中运行
pwd
```

**问题**: `PermissionError` 或 `OSError: [Errno 13] Permission denied`
**解决**: 给脚本执行权限
```bash
chmod +x client.py
chmod +x server.py
chmod +x simple_server.py
```

### 4. 网络问题

**问题**: 连接超时或网络错误
**解决**: 检查网络连接和防火墙设置
```bash
# 测试网络连接
ping google.com

# 检查端口是否被占用
lsof -i :8000
```

## 调试模式

### 1. 日志调试
调试脚本会自动创建 `debug.log` 文件，包含详细的执行日志：
```bash
# 查看日志
tail -f debug.log

# 查看错误日志
grep ERROR debug.log
```

### 2. 交互式调试
```bash
python debug_client.py 3
```

可用命令：
- `help` - 显示帮助
- `tools` - 列出可用工具
- `quit` - 退出调试

### 3. 分步调试
```bash
# 测试基本连接
python debug_client.py 1

# 测试网页爬虫
python debug_client.py 2
```

## 服务器测试

### 1. 测试简单服务器
```bash
# 直接运行简单服务器
python simple_server.py

# 在另一个终端测试
python test_simple.py
```

### 2. 测试爬虫服务器
```bash
# 直接运行爬虫服务器
python server.py

# 在另一个终端测试
python test_server.py
```

## 性能调试

### 1. 内存使用
```bash
# 监控内存使用
top -p $(pgrep -f "python.*client.py")

# 或者使用 ps
ps aux | grep python
```

### 2. 网络请求
```bash
# 监控网络连接
netstat -an | grep ESTABLISHED

# 使用 tcpdump 抓包（需要 root 权限）
sudo tcpdump -i lo0 port 8000
```

## 错误代码参考

| 错误代码 | 含义 | 解决方法 |
|---------|------|----------|
| `ModuleNotFoundError` | 模块未找到 | 安装缺失的依赖 |
| `AuthenticationError` | 认证失败 | 检查 API 密钥 |
| `FileNotFoundError` | 文件不存在 | 检查文件路径 |
| `PermissionError` | 权限不足 | 检查文件权限 |
| `ConnectionError` | 连接失败 | 检查网络和服务器 |
| `TimeoutError` | 超时 | 增加超时时间或检查网络 |

## 高级调试技巧

### 1. 使用 pdb 调试器
在代码中添加断点：
```python
import pdb; pdb.set_trace()
```

### 2. 使用 ipdb（更好的调试器）
```bash
pip install ipdb
```
然后在代码中：
```python
import ipdb; ipdb.set_trace()
```

### 3. 异步调试
对于异步代码，使用：
```python
import asyncio
import pdb

async def debug_function():
    await some_async_operation()
    pdb.set_trace()  # 在这里设置断点
```

### 4. 环境变量调试
```bash
# 设置调试环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
export DEBUG=1
export LOG_LEVEL=DEBUG
```

## 日志级别

可以通过修改 `debug_client.py` 中的日志级别来控制输出：

```python
# 只显示错误
logging.basicConfig(level=logging.ERROR)

# 显示警告和错误
logging.basicConfig(level=logging.WARNING)

# 显示所有信息（默认）
logging.basicConfig(level=logging.DEBUG)
```

## 联系支持

如果遇到无法解决的问题：
1. 检查 `debug.log` 文件中的详细错误信息
2. 确保所有依赖都已正确安装
3. 验证 API 密钥是否有效
4. 检查网络连接是否正常 