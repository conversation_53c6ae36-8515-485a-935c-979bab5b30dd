# 人民网新闻爬虫

这是一个简单的Python爬虫，用于爬取人民网首页的新闻标题和内容，并将数据保存为JSON格式。

## 功能特点

- 自动爬取人民网首页的新闻链接
- 提取新闻的标题和正文内容
- 将爬取的数据保存为JSON格式
- 内置随机延时，避免请求过于频繁
- 自动处理编码问题
- 多种选择器匹配，提高爬取成功率

## 环境要求

- Python 3.6+
- 依赖包：
  - requests
  - beautifulsoup4

## 安装依赖

```bash
pip install requests beautifulsoup4
```

## 使用方法

1. 确保已安装所有依赖包
2. 运行爬虫脚本：

```bash
··
```

3. 爬虫会自动开始工作，并在当前目录下生成`people_news.json`文件

## 数据格式

爬取的数据将保存为JSON格式，每条新闻包含以下字段：

```json
{
  "title": "新闻标题",
  "url": "新闻链接",
  "content": "新闻正文内容",
  "crawl_time": "爬取时间"
}
```

## 注意事项

- 本爬虫仅用于学习和研究目的
- 请勿频繁运行爬虫，以免对目标网站造成压力
- 爬取的内容版权归原网站所有
