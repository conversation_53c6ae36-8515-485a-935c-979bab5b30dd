# server.py 调试指南

## 快速开始

### 1. 直接功能测试（推荐）
```bash
# 直接测试服务器功能，不依赖 AI 模型
python test_server_direct.py
```

### 2. 使用专用调试脚本
```bash
# 运行所有测试
python debug_server.py 1

# 交互式调试
python debug_server.py 2

# 仅测试连接
python debug_server.py 3

# 测试工具功能
python debug_server.py 4
```

### 3. 使用现有测试脚本
```bash
# 运行完整测试套件
python test_server.py

# 运行简单测试
python test_simple.py
```

### 4. 直接运行服务器
```bash
# 直接运行服务器（用于调试服务器本身）
python server.py
```

## 服务器功能概述

`server.py` 提供以下工具：

### 1. fetch_url
- **功能**: 获取网页内容
- **参数**: `url` (字符串), `timeout` (整数，默认10秒)
- **返回**: 包含状态码、内容、编码等信息的字典

### 2. parse_html
- **功能**: 解析HTML内容
- **参数**: `html_content` (字符串), `selectors` (可选字典)
- **返回**: 解析后的文本内容和提取的数据

### 3. scrape_url
- **功能**: 一站式网页抓取和解析
- **参数**: `url` (字符串), `selectors` (可选), `timeout` (整数)
- **返回**: 结合了fetch和parse的结果

### 4. save_json
- **功能**: 保存数据到JSON文件
- **参数**: `data` (任意数据), `filename` (字符串)
- **返回**: 保存结果信息

### 5. load_json
- **功能**: 从JSON文件加载数据
- **参数**: `filename` (字符串)
- **返回**: 加载的数据

## 调试方法

### 1. 直接功能测试（最可靠）
```bash
python test_server_direct.py
```
这个脚本直接调用服务器工具，不依赖 AI 模型，可以快速验证服务器功能是否正常。

**测试内容**：
- ✅ fetch_url 工具测试
- ✅ parse_html 工具测试  
- ✅ scrape_url 工具测试
- ✅ JSON 文件操作测试
- ✅ 错误处理测试
- ✅ 性能测试

### 2. 日志调试
调试脚本会自动创建 `server_debug.log` 文件：
```bash
# 实时查看日志
tail -f server_debug.log

# 查看错误日志
grep ERROR server_debug.log

# 查看特定工具的日志
grep "fetch_url" server_debug.log
```

### 3. 分步调试
```bash
# 步骤1: 直接功能测试
python test_server_direct.py

# 步骤2: 测试连接
python debug_server.py 3

# 步骤3: 测试基本工具
python debug_server.py 4

# 步骤4: 运行完整测试
python debug_server.py 1
```

### 4. 交互式调试
```bash
python debug_server.py 2
```

可用命令：
- `help` - 显示帮助
- `tools` - 列出可用工具
- `test` - 运行所有测试
- `quit` - 退出调试

## 常见问题排查

### 1. 连接问题

**问题**: `FileNotFoundError: 服务器文件不存在`
**解决**: 
```bash
# 检查文件是否存在
ls -la server.py

# 检查文件权限
chmod +x server.py
```

**问题**: `ModuleNotFoundError: No module named 'mcp'`
**解决**: 
```bash
# 安装依赖
uv sync
# 或
pip install mcp fastmcp
```

### 2. API 密钥问题

**问题**: `PermissionDeniedError: Error code: 403`
**解决**: 
```bash
# 检查 API 密钥
echo $ANTHROPIC_API_KEY

# 设置 API 密钥
export ANTHROPIC_API_KEY=your_api_key_here

# 或者在 .env 文件中设置
echo "ANTHROPIC_API_KEY=your_api_key_here" > .env
```

**注意**: 如果 API 密钥有问题，可以使用 `test_server_direct.py` 进行功能测试，它不依赖 AI 模型。

### 3. 网络问题

**问题**: 网络请求超时
**解决**: 
```bash
# 测试网络连接
curl -I https://httpbin.org/json

# 检查防火墙设置
sudo ufw status
```

**问题**: SSL证书错误
**解决**: 在服务器代码中添加SSL验证跳过（仅用于测试）

### 4. 工具调用问题

**问题**: 工具返回错误
**解决**: 
```bash
# 查看详细错误日志
grep -A 10 -B 10 "ERROR" server_debug.log

# 测试单个工具
python debug_server.py 4

# 直接测试（推荐）
python test_server_direct.py
```

### 5. 性能问题

**问题**: 请求速度慢
**解决**: 
```bash
# 运行性能测试
python test_server_direct.py

# 检查网络延迟
ping httpbin.org
```

## 测试用例

### 1. 基本功能测试
```python
# 测试 fetch_url
query = """
Use the fetch_url tool to get content from "https://httpbin.org/json" 
and show me the response status.
"""

# 测试 parse_html
query = """
Use the scrape_url tool to fetch content from "https://httpbin.org/html" 
and extract all paragraph text using CSS selector "p".
"""

# 测试 JSON 操作
query = """
Use the save_json tool to save this data to "test.json":
{"message": "Hello", "timestamp": "2024-01-01"}
"""
```

### 2. 错误处理测试
```python
# 测试无效URL
query = """
Use the fetch_url tool to get content from "https://invalid-url.com"
"""

# 测试无效文件
query = """
Use the load_json tool to load data from "non-existent.json"
"""
```

### 3. 性能测试
```python
# 测试多个并发请求
urls = [
    "https://httpbin.org/json",
    "https://httpbin.org/html",
    "https://httpbin.org/xml"
]
```

## 调试技巧

### 1. 添加断点
在 `server.py` 中添加调试断点：
```python
import pdb; pdb.set_trace()
```

### 2. 增加日志
在服务器代码中添加详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

@mcp.tool()
def fetch_url(url: str, timeout: int = 10) -> Dict[str, Any]:
    logger.debug(f"Fetching URL: {url}")
    # ... 代码 ...
```

### 3. 监控资源使用
```bash
# 监控内存使用
top -p $(pgrep -f "python.*server.py")

# 监控网络连接
netstat -an | grep ESTABLISHED
```

### 4. 使用代理调试
```bash
# 使用 mitmproxy 调试网络请求
mitmproxy -p 8080
# 然后设置代理环境变量
export HTTP_PROXY=http://localhost:8080
export HTTPS_PROXY=http://localhost:8080
```

## 性能优化

### 1. 连接池
```python
# 在服务器中使用连接池
session = requests.Session()
session.mount('http://', requests.adapters.HTTPAdapter(pool_connections=10))
session.mount('https://', requests.adapters.HTTPAdapter(pool_connections=10))
```

### 2. 超时设置
```python
# 设置合理的超时时间
response = session.get(url, timeout=(5, 30))  # (连接超时, 读取超时)
```

### 3. 缓存机制
```python
# 添加简单的内存缓存
import functools
from typing import Dict, Any

_cache: Dict[str, Any] = {}

@functools.lru_cache(maxsize=100)
def cached_fetch_url(url: str) -> Dict[str, Any]:
    # ... 实现 ...
```

## 错误代码参考

| 错误类型 | 含义 | 解决方法 |
|---------|------|----------|
| `ConnectionError` | 连接失败 | 检查网络和URL |
| `TimeoutError` | 请求超时 | 增加超时时间 |
| `HTTPError` | HTTP错误 | 检查状态码 |
| `JSONDecodeError` | JSON解析错误 | 检查数据格式 |
| `FileNotFoundError` | 文件不存在 | 检查文件路径 |
| `PermissionError` | 权限不足 | 检查文件权限 |
| `PermissionDeniedError` | API密钥错误 | 检查API密钥设置 |

## 高级调试

### 1. 使用 strace 跟踪系统调用
```bash
strace -f -e trace=network python server.py
```

### 2. 使用 gdb 调试（如果编译了调试版本）
```bash
gdb python
(gdb) run server.py
```

### 3. 使用 cProfile 分析性能
```bash
python -m cProfile -o profile.stats server.py
python -c "import pstats; pstats.Stats('profile.stats').sort_stats('cumulative').print_stats(10)"
```

## 联系支持

如果遇到无法解决的问题：
1. 首先运行 `python test_server_direct.py` 检查服务器功能
2. 检查 `server_debug.log` 中的详细错误信息
3. 确保所有依赖都已正确安装
4. 验证网络连接是否正常
5. 检查服务器文件是否有语法错误 