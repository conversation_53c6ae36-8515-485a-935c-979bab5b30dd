2025-06-27 10:34:44,873 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-27 10:34:44,986 - DebugMCPClient - INFO - 尝试连接到服务器: /Users/<USER>/Downloads/MCP_TEST/mcp-client/simple_server.py
2025-06-27 10:34:44,987 - DebugMCPClient - INFO - 服务器文件存在，大小: 2931 bytes
2025-06-27 10:34:45,755 - DebugMCPClient - ERROR - 连接服务器失败: 3 validation errors for ListToolsResult
tools.0
  Input should be a valid dictionary or instance of Tool [type=model_type, input_value=('meta', None), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
tools.1
  Input should be a valid dictionary or instance of Tool [type=model_type, input_value=('nextCursor', None), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
tools.2
  Input should be a valid dictionary or instance of Tool [type=model_type, input_value=('tools', [Tool(name='ech...']}, annotations=None)]), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
2025-06-27 10:34:45,762 - DebugMCPClient - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/debug_client.py", line 49, in connect_to_server
    await super().connect_to_server(server_script_path)
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/client.py", line 46, in connect_to_server
    response = await self.session.list_tools()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/mcp/client/session.py", line 324, in list_tools
    return await self.send_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/mcp/shared/session.py", line 286, in send_request
    raise McpError(response_or_error.error)
mcp.shared.exceptions.McpError: 3 validation errors for ListToolsResult
tools.0
  Input should be a valid dictionary or instance of Tool [type=model_type, input_value=('meta', None), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
tools.1
  Input should be a valid dictionary or instance of Tool [type=model_type, input_value=('nextCursor', None), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
tools.2
  Input should be a valid dictionary or instance of Tool [type=model_type, input_value=('tools', [Tool(name='ech...']}, annotations=None)]), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type

2025-06-27 10:40:47,649 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-27 10:40:51,712 - DebugMCPClient - INFO - 尝试连接到服务器: /Users/<USER>/Downloads/MCP_TEST/mcp-client/simple_server.py
2025-06-27 10:40:51,712 - DebugMCPClient - INFO - 服务器文件存在，大小: 2931 bytes
2025-06-27 10:40:52,411 - DebugMCPClient - ERROR - 连接服务器失败: 3 validation errors for ListToolsResult
tools.0
  Input should be a valid dictionary or instance of Tool [type=model_type, input_value=('meta', None), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
tools.1
  Input should be a valid dictionary or instance of Tool [type=model_type, input_value=('nextCursor', None), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
tools.2
  Input should be a valid dictionary or instance of Tool [type=model_type, input_value=('tools', [Tool(name='ech...']}, annotations=None)]), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
2025-06-27 10:40:52,420 - DebugMCPClient - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/debug_client.py", line 49, in connect_to_server
    await super().connect_to_server(server_script_path)
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/client.py", line 46, in connect_to_server
    response = await self.session.list_tools()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/mcp/client/session.py", line 324, in list_tools
    return await self.send_request(
           ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/.venv/lib/python3.13/site-packages/mcp/shared/session.py", line 286, in send_request
    raise McpError(response_or_error.error)
mcp.shared.exceptions.McpError: 3 validation errors for ListToolsResult
tools.0
  Input should be a valid dictionary or instance of Tool [type=model_type, input_value=('meta', None), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
tools.1
  Input should be a valid dictionary or instance of Tool [type=model_type, input_value=('nextCursor', None), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type
tools.2
  Input should be a valid dictionary or instance of Tool [type=model_type, input_value=('tools', [Tool(name='ech...']}, annotations=None)]), input_type=tuple]
    For further information visit https://errors.pydantic.dev/2.11/v/model_type

2025-06-27 10:45:53,489 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-27 10:45:55,895 - DebugMCPClient - INFO - 尝试连接到服务器: /Users/<USER>/Downloads/MCP_TEST/mcp-client/simple_server.py
2025-06-27 10:45:55,895 - DebugMCPClient - INFO - 服务器文件存在，大小: 2931 bytes
2025-06-27 10:48:11,882 - DebugMCPClient - ERROR - 连接服务器失败: 
2025-06-27 10:48:11,892 - DebugMCPClient - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Downloads/MCP_TEST/mcp-client/debug_client.py", line 49, in connect_to_server
    await super().connect_to_server(server_script_path)
  File "/Users/<USER>/.pyenv/versions/3.13.5/lib/python3.13/bdb.py", line 108, in trace_dispatch
    return self.dispatch_exception(frame, arg)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.13.5/lib/python3.13/bdb.py", line 195, in dispatch_exception
    if self.quitting: raise BdbQuit
                      ^^^^^^^^^^^^^
bdb.BdbQuit

